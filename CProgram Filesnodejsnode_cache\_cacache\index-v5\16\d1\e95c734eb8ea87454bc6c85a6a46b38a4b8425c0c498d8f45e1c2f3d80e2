
386043d96a5e91d38bd1c52f52b7f85072e1805f	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","integrity":"sha512-9kWBfAIbvRvfqknziHww6xzocf4IqcGwwWJlB63IQkmgCzfpDuWEJFQ930fHOoafEX5cn9Xij7Ub73xDM+4sxw==","time":1754898857145,"size":442150,"metadata":{"time":1754898857145,"url":"https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Mon, 11 Aug 2025 07:49:40 GMT","etag":"W/\"6c4e2c2fae12901f5f086f10f1276cfa19598904\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}
b2f3d726ada2f3c55127b04107be96ec8d5efa98	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","integrity":"sha512-9kWBfAIbvRvfqknziHww6xzocf4IqcGwwWJlB63IQkmgCzfpDuWEJFQ930fHOoafEX5cn9Xij7Ub73xDM+4sxw==","time":1754904171053,"size":442150,"metadata":{"time":1754904171053,"url":"https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Mon, 11 Aug 2025 09:19:44 GMT","etag":"W/\"6c4e2c2fae12901f5f086f10f1276cfa19598904\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}