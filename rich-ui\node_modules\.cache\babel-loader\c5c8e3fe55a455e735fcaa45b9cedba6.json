{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue", "mtime": 1754903721862}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX3RvQ29uc3VtYWJsZUFycmF5MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovVXNlcnMvQWRtaW5pc3RyYXRvci9JZGVhUHJvamVjdHMvcmljaC10ZXN0L3JpY2gtdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvdG9Db25zdW1hYmxlQXJyYXkuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKdmFyIF9jdXJyZW5jeSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiY3VycmVuY3kuanMiKSk7CnZhciBfdnVlVHJlZXNlbGVjdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiKSk7CnZhciBfanNQaW55aW4gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoImpzLXBpbnlpbiIpKTsKdmFyIF9pbmRleCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9jb21wb25lbnRzL0NvbXBhbnlTZWxlY3QvaW5kZXgudnVlIikpOwp2YXIgX3JpY2ggPSByZXF1aXJlKCJAL3V0aWxzL3JpY2giKTsKdmFyIF9zb3J0YWJsZWpzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJzb3J0YWJsZWpzIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogImRlYml0Tm90ZUNoYXJnZUxpc3QiLAogIGNvbXBvbmVudHM6IHsKICAgIENvbXBhbnlTZWxlY3Q6IF9pbmRleC5kZWZhdWx0LAogICAgVHJlZXNlbGVjdDogX3Z1ZVRyZWVzZWxlY3QuZGVmYXVsdAogIH0sCiAgcHJvcHM6IFsiY2hhcmdlRGF0YSIsICJjb21wYW55TGlzdCIsICJvcGVuQ2hhcmdlTGlzdCIsICJpc1JlY2VpdmFibGUiLCAiZGlzYWJsZWQiLCAiaGlkZGVuU3VwcGxpZXIiLCAicnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVRheFVTRCIsICJyc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1CIiwgInJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhVU0QiLCAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFJNQiIsICJyc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlUk1CIiwgInJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVVU0QiLCAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVJNQiIsICJyc0NsaWVudE1lc3NhZ2VQYXlhYmxlVVNEIiwgInJzQ2xpZW50TWVzc2FnZVByb2ZpdCIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXROb1RheCIsICJwYXlEZXRhaWxSTUIiLCAicGF5RGV0YWlsVVNEIiwgInBheURldGFpbFJNQlRheCIsICJwYXlEZXRhaWxVU0RUYXgiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0VVNEIiwgInJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQiIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhSTUIiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0VGF4VVNEIiwgImRlYml0Tm90ZSIsICJkcmFnR3JvdXBOYW1lIl0sCiAgY29tcHV0ZWQ6IHsKICAgIGhhc0NvbmZpcm1Sb3c6IGZ1bmN0aW9uIGhhc0NvbmZpcm1Sb3coKSB7CiAgICAgIHZhciByZXN1bHQgPSBmYWxzZTsKICAgICAgdGhpcy5jaGFyZ2VEYXRhICYmIHRoaXMuY2hhcmdlRGF0YS5sZW5ndGggPiAwID8gdGhpcy5jaGFyZ2VEYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLmlzQWNjb3VudENvbmZpcm1lZCA9PT0gIjEiKSB7CiAgICAgICAgICByZXN1bHQgPSB0cnVlOwogICAgICAgIH0KICAgICAgfSkgOiBudWxsOwogICAgICByZXR1cm4gcmVzdWx0OwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIGNoYXJnZURhdGE6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIGlmICghb2xkVmFsKSB7CiAgICAgICAgICB0aGlzLiRlbWl0KCJyZXR1cm4iLCBuZXdWYWwpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KCiAgICAgICAgLy8g6YGN5Y6G6LS555So5YiX6KGo77yM5qOA5p+l5biB56eN5Y+Y5YyWCiAgICAgICAgbmV3VmFsID8gbmV3VmFsLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICB2YXIgb2xkSXRlbSA9IG9sZFZhbFtpbmRleF07CgogICAgICAgICAgLy8g5qOA5p+l5biB56eN5Y+Y5YyW5bm26K6h566X5bCP6K6hCiAgICAgICAgICBpZiAoaXRlbS5jdXJyZW5jeSAmJiBpdGVtLmFtb3VudCkgewogICAgICAgICAgICAvLyDlpoLmnpzku44gUk1CIOaNouaIkCBVU0TvvIzkvb/nlKggMS/msYfnjocg6K6h566XCiAgICAgICAgICAgIGlmIChvbGRJdGVtICYmIG9sZEl0ZW0uY3VycmVuY3kgPT09ICJSTUIiICYmIGl0ZW0uY3VycmVuY3kgPT09ICJVU0QiKSB7CiAgICAgICAgICAgICAgaWYgKGl0ZW0uZXhjaGFuZ2VSYXRlICYmIGl0ZW0uZXhjaGFuZ2VSYXRlICE9PSAwKSB7CiAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAvLyDorqHnrpcgMS/msYfnjofvvIzkv53nlZk05L2N5bCP5pWwCiAgICAgICAgICAgICAgICAgIHZhciBpbnZlcnNlUmF0ZSA9ICgwLCBfY3VycmVuY3kuZGVmYXVsdCkoMSwgewogICAgICAgICAgICAgICAgICAgIHByZWNpc2lvbjogNAogICAgICAgICAgICAgICAgICB9KS5kaXZpZGUoaXRlbS5leGNoYW5nZVJhdGUpLnZhbHVlOwoKICAgICAgICAgICAgICAgICAgLy8g6K6h566X5bCP6K6hOiDljZXku7cgKiDmlbDph48gKiDmsYfnjocgKiAoMSArIOeojueOhy8xMDApCiAgICAgICAgICAgICAgICAgIGl0ZW0uc3VidG90YWwgPSAoMCwgX2N1cnJlbmN5LmRlZmF1bHQpKGl0ZW0uZG5Vbml0UmF0ZSB8fCAwLCB7CiAgICAgICAgICAgICAgICAgICAgcHJlY2lzaW9uOiA0CiAgICAgICAgICAgICAgICAgIH0pLm11bHRpcGx5KGl0ZW0uYW1vdW50KS5tdWx0aXBseShpbnZlcnNlUmF0ZSkubXVsdGlwbHkoKDAsIF9jdXJyZW5jeS5kZWZhdWx0KSgxKS5hZGQoKDAsIF9jdXJyZW5jeS5kZWZhdWx0KShpdGVtLmR1dHlSYXRlIHx8IDApLmRpdmlkZSgxMDApKSkudmFsdWU7CiAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLorqHnrpflsI/orqHlh7rplJk6IiwgZXJyb3IpOwogICAgICAgICAgICAgICAgICBpdGVtLnN1YnRvdGFsID0gMDsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KSA6IG51bGw7CiAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgbmV3VmFsID8gbmV3VmFsIDogW10pOwoKICAgICAgICAvLyDmlbDmja7lj5jljJblkI7ph43mlrDliJ3lp4vljJbmi5bmi73vvIzkvb/nlKjmm7TlronlhajnmoTmlrnlvI8KICAgICAgICB0aGlzLnJlaW5pdGlhbGl6ZVNvcnRhYmxlKCk7CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfSwKICAgIGRpc2FibGVkOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgLy8g56aB55So54q25oCB5pS55Y+Y5pe26YeN5paw5Yid5aeL5YyW5ouW5ou9CiAgICAgICAgdGhpcy5yZWluaXRpYWxpemVTb3J0YWJsZSgpOwogICAgICB9CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0U29ydGFibGUoKTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICAvLyDmuIXnkIblrprml7blmagKICAgIGlmICh0aGlzLnJlaW5pdFRpbWVyKSB7CiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnJlaW5pdFRpbWVyKTsKICAgICAgdGhpcy5yZWluaXRUaW1lciA9IG51bGw7CiAgICB9CiAgICAvLyDplIDmr4Hmi5bmi73lrp7kvosKICAgIHRoaXMuZGVzdHJveVNvcnRhYmxlKCk7CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGF5VG90YWxSTUI6IDAsCiAgICAgIHBheVRvdGFsVVNEOiAwLAogICAgICBzaG93Q2xpZW50TmFtZTogbnVsbCwKICAgICAgc29ydGFibGU6IG51bGwsCiAgICAgIHJlaW5pdFRpbWVyOiBudWxsLAogICAgICBkcmFnU3RhcnRDb2x1bW46IC0xLAogICAgICAvLyDorrDlvZXmi5bmi73lvIDlp4vnmoTliJfntKLlvJUKICAgICAgc2VydmljZXM6IFt7CiAgICAgICAgdmFsdWU6IDEsCiAgICAgICAgbGFiZWw6ICLmtbfov5AiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogMTAsCiAgICAgICAgbGFiZWw6ICLnqbrov5AiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogMjAsCiAgICAgICAgbGFiZWw6ICLpk4Hot68iCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNDAsCiAgICAgICAgbGFiZWw6ICLlv6vpgJIiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNTAsCiAgICAgICAgbGFiZWw6ICLmi5bovaYiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNjAsCiAgICAgICAgbGFiZWw6ICLmiqXlhbMiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNzAsCiAgICAgICAgbGFiZWw6ICLmuIXlhbPmtL7pgIEiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogODAsCiAgICAgICAgbGFiZWw6ICLnoIHlpLTku5PlgqgiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogOTAsCiAgICAgICAgbGFiZWw6ICLmo4Dpqozor4HkuaYiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogMTAwLAogICAgICAgIGxhYmVsOiAi5L+d6ZmpIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDEwMSwKICAgICAgICBsYWJlbDogIuaJqeWxleacjeWKoSIKICAgICAgfV0sCiAgICAgIHNlcnZpY2U6IFt7CiAgICAgICAgdmFsdWU6IDEsCiAgICAgICAgbGFiZWw6ICLln7rnoYDmnI3liqEiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNCwKICAgICAgICBsYWJlbDogIuWJjeeoi+i/kOi+kyIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiA1LAogICAgICAgIGxhYmVsOiAi5Ye65Y+j5oql5YWzIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDYsCiAgICAgICAgbGFiZWw6ICLov5vlj6PmuIXlhbMiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogMiwKICAgICAgICBsYWJlbDogIua1t+i/kCIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAzLAogICAgICAgIGxhYmVsOiAi6ZmG6L+QIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDQsCiAgICAgICAgbGFiZWw6ICLpk4Hot68iCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNSwKICAgICAgICBsYWJlbDogIuepuui/kCIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiA2LAogICAgICAgIGxhYmVsOiAi5b+r6YCSIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDIxLAogICAgICAgIGxhYmVsOiAi5pW05p+c5rW36L+QIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDIyLAogICAgICAgIGxhYmVsOiAi5ou85p+c5rW36L+QIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDIzLAogICAgICAgIGxhYmVsOiAi5pWj5p2C6Ii5IgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDI0LAogICAgICAgIGxhYmVsOiAi5rua6KOF6Ii5IgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDQxLAogICAgICAgIGxhYmVsOiAi5pW05p+c6ZOB6LevIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDQyLAogICAgICAgIGxhYmVsOiAi5ou85p+c6ZOB6LevIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDQzLAogICAgICAgIGxhYmVsOiAi6ZOB6Lev6L2m55quIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDUxLAogICAgICAgIGxhYmVsOiAi56m66L+Q5pmu6IixIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDUyLAogICAgICAgIGxhYmVsOiAi56m66L+Q5YyF5p2/IgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDUzLAogICAgICAgIGxhYmVsOiAi56m66L+Q5YyF5py6IgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDU0LAogICAgICAgIGxhYmVsOiAi56m66L+Q6KGM5p2OIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDk2MSwKICAgICAgICBsYWJlbDogIuWJjeeoi+i/kOi+kyIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiA5NjQsCiAgICAgICAgbGFiZWw6ICLov5vlj6PmuIXlhbMiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNywKICAgICAgICBsYWJlbDogIuWHuuWPo+aKpeWFsyIKICAgICAgfV0sCiAgICAgIGNoYXJnZVJlbWFyazogbnVsbAogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGRlc3Ryb3lTb3J0YWJsZTogZnVuY3Rpb24gZGVzdHJveVNvcnRhYmxlKCkgewogICAgICBpZiAodGhpcy5zb3J0YWJsZSkgewogICAgICAgIHRyeSB7CiAgICAgICAgICAvLyDmo4Dmn6Ugc29ydGFibGUg5a6e5L6L5piv5ZCm6L+Y5pyJ5pWICiAgICAgICAgICBpZiAodGhpcy5zb3J0YWJsZS5lbCAmJiB0aGlzLnNvcnRhYmxlLmVsLnBhcmVudE5vZGUpIHsKICAgICAgICAgICAgdGhpcy5zb3J0YWJsZS5kZXN0cm95KCk7CiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUud2FybignRXJyb3IgZGVzdHJveWluZyBzb3J0YWJsZTonLCBlcnJvcik7CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIHRoaXMuc29ydGFibGUgPSBudWxsOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIHJlaW5pdGlhbGl6ZVNvcnRhYmxlOiBmdW5jdGlvbiByZWluaXRpYWxpemVTb3J0YWJsZSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgLy8g5L2/55So6Ziy5oqW5bu26L+f6YeN5paw5Yid5aeL5YyW77yM6YG/5YWN6aKR57mB55qE5Yib5bu66ZSA5q+BCiAgICAgIGlmICh0aGlzLnJlaW5pdFRpbWVyKSB7CiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVpbml0VGltZXIpOwogICAgICB9CiAgICAgIHRoaXMucmVpbml0VGltZXIgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMuZGVzdHJveVNvcnRhYmxlKCk7CiAgICAgICAgICAvLyDnoa7kv50gRE9NIOW3suabtOaWsOWQjuWGjeWIneWni+WMlgogICAgICAgICAgX3RoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXMuaW5pdFNvcnRhYmxlKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSwgMTAwKTsKICAgIH0sCiAgICBpbml0U29ydGFibGU6IGZ1bmN0aW9uIGluaXRTb3J0YWJsZSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBpZiAoX3RoaXMyLiRyZWZzLmNoYXJnZVRhYmxlICYmIF90aGlzMi4kcmVmcy5jaGFyZ2VUYWJsZS4kZWwpIHsKICAgICAgICAgIHZhciB0Ym9keSA9IF90aGlzMi4kcmVmcy5jaGFyZ2VUYWJsZS4kZWwucXVlcnlTZWxlY3RvcigndGJvZHknKTsKICAgICAgICAgIGlmICh0Ym9keSkgewogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgIF90aGlzMi5zb3J0YWJsZSA9IF9zb3J0YWJsZWpzLmRlZmF1bHQuY3JlYXRlKHRib2R5LCB7CiAgICAgICAgICAgICAgICBncm91cDogewogICAgICAgICAgICAgICAgICBuYW1lOiBfdGhpczIuZHJhZ0dyb3VwTmFtZSB8fCAnZGViaXROb3RlQ2hhcmdlR3JvdXAnLAogICAgICAgICAgICAgICAgICBwdWxsOiBmdW5jdGlvbiBwdWxsKHRvLCBmcm9tLCBkcmFnRWwsIGV2dCkgewogICAgICAgICAgICAgICAgICAgIC8vIOagueaNruaLluaLveWIl+WGs+WumuaYr+WQpuWFgeiuuOaLluWHuu+8iOWJquWIh+aIluWkjeWItu+8iQogICAgICAgICAgICAgICAgICAgIGlmIChfdGhpczIuZHJhZ1N0YXJ0Q29sdW1uID09PSAxKSB7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTsgLy8g5LuO56ys5LiA5YiX5ouW5Yqo5pe25YWB6K645ouW5Ye677yI5Ymq5YiH77yJCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnY2xvbmUnOyAvLyDku47lhbbku5bliJfmi5bliqjml7blpI3liLYKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0sCgogICAgICAgICAgICAgICAgICBwdXQ6ICFfdGhpczIuZGlzYWJsZWQKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBhbmltYXRpb246IDE1MCwKICAgICAgICAgICAgICAgIGRpc2FibGVkOiBfdGhpczIuZGlzYWJsZWQsCiAgICAgICAgICAgICAgICBnaG9zdENsYXNzOiAnc29ydGFibGUtZ2hvc3QnLAogICAgICAgICAgICAgICAgZHJhZ0NsYXNzOiAnc29ydGFibGUtZHJhZycsCiAgICAgICAgICAgICAgICBmaWx0ZXI6ICcuZGlzYWJsZWQnLAogICAgICAgICAgICAgICAgb25TdGFydDogZnVuY3Rpb24gb25TdGFydChldnQpIHsKICAgICAgICAgICAgICAgICAgLy8g5ouW5ou95byA5aeLCiAgICAgICAgICAgICAgICAgIGlmICghX3RoaXMyLmNoYXJnZURhdGEgfHwgIV90aGlzMi5jaGFyZ2VEYXRhLmxlbmd0aCkgcmV0dXJuOwogICAgICAgICAgICAgICAgICB2YXIgZHJhZ2dlZEl0ZW0gPSBfdGhpczIuY2hhcmdlRGF0YVtldnQub2xkSW5kZXhdOwoKICAgICAgICAgICAgICAgICAgLy8g6I635Y+W5ouW5ou95byA5aeL55qE5YiX57Si5byVCiAgICAgICAgICAgICAgICAgIF90aGlzMi5kcmFnU3RhcnRDb2x1bW4gPSAtMTsgLy8g6buY6K6k6K6+572u5Li6LTEKICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAvLyDojrflj5bmi5bmi73kuovku7bnmoTotbflp4vlnZDmoIcKICAgICAgICAgICAgICAgICAgICB2YXIgbW91c2VYID0gZXZ0Lm9yaWdpbmFsRXZlbnQuY2xpZW50WDsKICAgICAgICAgICAgICAgICAgICB2YXIgbW91c2VZID0gZXZ0Lm9yaWdpbmFsRXZlbnQuY2xpZW50WTsKCiAgICAgICAgICAgICAgICAgICAgLy8g5bCd6K+V5pu05YeG56Gu5Zyw56Gu5a6a5ouW5ou95byA5aeL55qE5YiXCiAgICAgICAgICAgICAgICAgICAgdmFyIGNlbGxzID0gZXZ0Lml0ZW0ucXVlcnlTZWxlY3RvckFsbCgndGQnKTsKICAgICAgICAgICAgICAgICAgICBpZiAoY2VsbHMgJiYgY2VsbHMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgICAgLy8g6K6h566X5q+P5Liq5Y2V5YWD5qC855qE5L2N572u77yM5om+5Yiw5YyF5ZCr6byg5qCH5L2N572u55qE5Y2V5YWD5qC8CiAgICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGNlbGxzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHZhciByZWN0ID0gY2VsbHNbaV0uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtb3VzZVggPj0gcmVjdC5sZWZ0ICYmIG1vdXNlWCA8PSByZWN0LnJpZ2h0ICYmIG1vdXNlWSA+PSByZWN0LnRvcCAmJiBtb3VzZVkgPD0gcmVjdC5ib3R0b20pIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczIuZHJhZ1N0YXJ0Q29sdW1uID0gaTsKICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgLy8g5aSH6YCJ5pa55rOV77ya5aaC5p6c5LiK6Z2i55qE5pa55rOV5rKh5om+5Yiw77yM5L2/55So6KGo5aS05a6a5L2NCiAgICAgICAgICAgICAgICAgICAgaWYgKF90aGlzMi5kcmFnU3RhcnRDb2x1bW4gPT09IC0xKSB7CiAgICAgICAgICAgICAgICAgICAgICB2YXIgaGVhZGVyQ2VsbHMgPSBfdGhpczIuJHJlZnMuY2hhcmdlVGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3JBbGwoJ3RoZWFkIHRoJyk7CiAgICAgICAgICAgICAgICAgICAgICBpZiAoaGVhZGVyQ2VsbHMgJiYgaGVhZGVyQ2VsbHMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgaGVhZGVyQ2VsbHMubGVuZ3RoOyBfaSsrKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIF9yZWN0ID0gaGVhZGVyQ2VsbHNbX2ldLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpOwogICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtb3VzZVggPj0gX3JlY3QubGVmdCAmJiBtb3VzZVggPD0gX3JlY3QucmlnaHQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzMi5kcmFnU3RhcnRDb2x1bW4gPSBfaTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICBpZiAoX3RoaXMyLmRyYWdTdGFydENvbHVtbiA9PT0gLTEpIHsKICAgICAgICAgICAgICAgICAgICAgIC8vIOWbnumAgOaWueahiO+8muWmguaenOmAmui/h+WdkOagh+aXoOazleehruWumu+8jOWImem7mOiupOS4uumdnuesrOS4gOWIlwogICAgICAgICAgICAgICAgICAgICAgX3RoaXMyLmRyYWdTdGFydENvbHVtbiA9IDI7IC8vIOiuvue9ruS4uumdnuesrOS4gOWIl++8jOm7mOiupOS4uuWkjeWItuaooeW8jwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfnoa7lrprmi5bmi73lvIDlp4vliJfml7blh7rplJk6JywgZXJyb3IpOwogICAgICAgICAgICAgICAgICAgIF90aGlzMi5kcmFnU3RhcnRDb2x1bW4gPSAxOyAvLyDlh7rplJnml7bpu5jorqTkuLrnrKzkuIDliJcKICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgLy8g6K6+572u6KKr5ouW5ou95YWD57Sg55qE5pWw5o2uCiAgICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgICAgZXZ0Lml0ZW0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScsIEpTT04uc3RyaW5naWZ5KGRyYWdnZWRJdGVtKSk7CiAgICAgICAgICAgICAgICAgICAgLy8g6aKd5aSW5re75Yqg5ouW5ou96LW35aeL5YiX5L+h5oGvCiAgICAgICAgICAgICAgICAgICAgZXZ0Lml0ZW0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctY29sdW1uJywgX3RoaXMyLmRyYWdTdGFydENvbHVtbik7CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0cmluZ2lmeSBkcmFnIGl0ZW06JywgZXJyb3IpOwogICAgICAgICAgICAgICAgICAgIGV2dC5pdGVtLnNldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWl0ZW0nLCAne30nKTsKICAgICAgICAgICAgICAgICAgICBldnQuaXRlbS5zZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1jb2x1bW4nLCAnLTEnKTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBfdGhpczIuJGVtaXQoJ2RyYWdTdGFydCcsIHsKICAgICAgICAgICAgICAgICAgICBpdGVtOiBkcmFnZ2VkSXRlbSwKICAgICAgICAgICAgICAgICAgICBpbmRleDogZXZ0Lm9sZEluZGV4LAogICAgICAgICAgICAgICAgICAgIGZyb206IF90aGlzMiwKICAgICAgICAgICAgICAgICAgICBjb2x1bW46IF90aGlzMi5kcmFnU3RhcnRDb2x1bW4KICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgb25BZGQ6IGZ1bmN0aW9uIG9uQWRkKGV2dCkgewogICAgICAgICAgICAgICAgICAvLyDmjqXmlLbliLDmlrDlhYPntKAKICAgICAgICAgICAgICAgICAgaWYgKCFfdGhpczIuY2hhcmdlRGF0YSkgcmV0dXJuOwogICAgICAgICAgICAgICAgICB2YXIgaXRlbSA9IGV2dC5pdGVtOwogICAgICAgICAgICAgICAgICB2YXIgZHJhZ2dlZEl0ZW0gPSB7fTsKICAgICAgICAgICAgICAgICAgdmFyIGRyYWdTdGFydENvbHVtbiA9IC0xOwogICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgIHZhciBkcmFnRGF0YSA9IGl0ZW0uZ2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScpOwogICAgICAgICAgICAgICAgICAgIGlmIChkcmFnRGF0YSAmJiBkcmFnRGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHsKICAgICAgICAgICAgICAgICAgICAgIGRyYWdnZWRJdGVtID0gSlNPTi5wYXJzZShkcmFnRGF0YSk7CiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAvLyDojrflj5bmi5bmi73otbflp4vliJcKICAgICAgICAgICAgICAgICAgICB2YXIgY29sdW1uRGF0YSA9IGl0ZW0uZ2V0QXR0cmlidXRlKCdkYXRhLWRyYWctY29sdW1uJyk7CiAgICAgICAgICAgICAgICAgICAgaWYgKGNvbHVtbkRhdGEgJiYgY29sdW1uRGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHsKICAgICAgICAgICAgICAgICAgICAgIGRyYWdTdGFydENvbHVtbiA9IHBhcnNlSW50KGNvbHVtbkRhdGEsIDEwKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHBhcnNlIGRyYWcgaXRlbSBkYXRhOicsIGVycm9yKTsKICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgLy8g5aSE55CG5paw5aKe5YWD57Sg5Yiw6KGo5qC8CiAgICAgICAgICAgICAgICAgIHZhciBuZXdDaGFyZ2VEYXRhID0gKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoX3RoaXMyLmNoYXJnZURhdGEpOwoKICAgICAgICAgICAgICAgICAgLy8g5peg6K665piv5aSN5Yi26L+Y5piv5Ymq5YiH77yM6YO96ZyA6KaB5re75Yqg6aG55Yiw55uu5qCH5L2N572uCiAgICAgICAgICAgICAgICAgIC8vIOS9huimgee7meaWsOmhueeUn+aIkOS4gOS4quaWsOeahElE77yM6KGo56S66L+Z5piv5LiA5Liq5YWo5paw55qE6aG5CiAgICAgICAgICAgICAgICAgIG5ld0NoYXJnZURhdGEuc3BsaWNlKGV2dC5uZXdJbmRleCwgMCwgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgZHJhZ2dlZEl0ZW0pLCB7fSwgewogICAgICAgICAgICAgICAgICAgIHRlbXBJZDogInRlbXBfIi5jb25jYXQoRGF0ZS5ub3coKSwgIl8iKS5jb25jYXQoTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpKSwKICAgICAgICAgICAgICAgICAgICBjaGFyZ2VJZDogbnVsbCAvLyDmuIXpmaTljp/mnIlJRO+8jOS9nOS4uuaWsOWinumhuQogICAgICAgICAgICAgICAgICB9KSk7CgogICAgICAgICAgICAgICAgICBfdGhpczIuJGVtaXQoJ3JldHVybicsIG5ld0NoYXJnZURhdGEpOwogICAgICAgICAgICAgICAgICBfdGhpczIuJGVtaXQoJ2RyYWdBZGQnLCB7CiAgICAgICAgICAgICAgICAgICAgaXRlbTogZHJhZ2dlZEl0ZW0sCiAgICAgICAgICAgICAgICAgICAgbmV3SW5kZXg6IGV2dC5uZXdJbmRleCwKICAgICAgICAgICAgICAgICAgICB0bzogX3RoaXMyLAogICAgICAgICAgICAgICAgICAgIGNvbHVtbjogZHJhZ1N0YXJ0Q29sdW1uCiAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgLy8g5ouW5ou95re75Yqg5ZCO5Yi35paw6KGo5qC8CiAgICAgICAgICAgICAgICAgIF90aGlzMi5yZWZyZXNoVGFibGUoKTsKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBvblJlbW92ZTogZnVuY3Rpb24gb25SZW1vdmUoZXZ0KSB7CiAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluaLluaLveS/oeaBrwogICAgICAgICAgICAgICAgICBpZiAoIV90aGlzMi5jaGFyZ2VEYXRhKSByZXR1cm47CiAgICAgICAgICAgICAgICAgIHZhciBpdGVtID0gZXZ0Lml0ZW07CiAgICAgICAgICAgICAgICAgIHZhciBkcmFnU3RhcnRDb2x1bW4gPSAtMTsKICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICB2YXIgY29sdW1uRGF0YSA9IGl0ZW0uZ2V0QXR0cmlidXRlKCdkYXRhLWRyYWctY29sdW1uJyk7CiAgICAgICAgICAgICAgICAgICAgaWYgKGNvbHVtbkRhdGEgJiYgY29sdW1uRGF0YSAhPT0gJ3VuZGVmaW5lZCcpIHsKICAgICAgICAgICAgICAgICAgICAgIGRyYWdTdGFydENvbHVtbiA9IHBhcnNlSW50KGNvbHVtbkRhdGEsIDEwKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHBhcnNlIGRyYWcgY29sdW1uIGRhdGE6JywgZXJyb3IpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIHZhciBuZXdDaGFyZ2VEYXRhID0gKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoX3RoaXMyLmNoYXJnZURhdGEpOwogICAgICAgICAgICAgICAgICB2YXIgcmVtb3ZlZEl0ZW0gPSBuZXdDaGFyZ2VEYXRhW2V2dC5vbGRJbmRleF07CgogICAgICAgICAgICAgICAgICAvLyDlj6rmnInlnKjku47nrKzkuIDliJflvIDlp4vmi5bmi73ml7bmiY3miafooYzliarliIfmk43kvZwKICAgICAgICAgICAgICAgICAgLy8gU29ydGFibGXnmoRjbG9uZemAiemhueW3sue7j+aOp+WItuS6huWkjeWItuihjOS4uu+8jOi/memHjOaIkeS7rOWPqumcgOWkhOeQhuWJquWIh+eahOaDheWGtQogICAgICAgICAgICAgICAgICBpZiAoZHJhZ1N0YXJ0Q29sdW1uID09PSAxKSB7CiAgICAgICAgICAgICAgICAgICAgbmV3Q2hhcmdlRGF0YS5zcGxpY2UoZXZ0Lm9sZEluZGV4LCAxKTsKICAgICAgICAgICAgICAgICAgICBfdGhpczIuJGVtaXQoJ3JldHVybicsIG5ld0NoYXJnZURhdGEpOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kZW1pdCgnZHJhZ1JlbW92ZScsIHsKICAgICAgICAgICAgICAgICAgICBpdGVtOiByZW1vdmVkSXRlbSwKICAgICAgICAgICAgICAgICAgICBvbGRJbmRleDogZXZ0Lm9sZEluZGV4LAogICAgICAgICAgICAgICAgICAgIGZyb206IF90aGlzMiwKICAgICAgICAgICAgICAgICAgICBjb2x1bW46IGRyYWdTdGFydENvbHVtbiwKICAgICAgICAgICAgICAgICAgICBpc0N1dDogZHJhZ1N0YXJ0Q29sdW1uID09PSAxCiAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgLy8g5ouW5ou956e76Zmk5ZCO5Yi35paw6KGo5qC8CiAgICAgICAgICAgICAgICAgIF90aGlzMi5yZWZyZXNoVGFibGUoKTsKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBvblVwZGF0ZTogZnVuY3Rpb24gb25VcGRhdGUoZXZ0KSB7CiAgICAgICAgICAgICAgICAgIC8vIOWQjOS4gOihqOagvOWGheaOkuW6jwogICAgICAgICAgICAgICAgICBpZiAoIV90aGlzMi5jaGFyZ2VEYXRhKSByZXR1cm47CiAgICAgICAgICAgICAgICAgIHZhciBuZXdDaGFyZ2VEYXRhID0gKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoX3RoaXMyLmNoYXJnZURhdGEpOwogICAgICAgICAgICAgICAgICB2YXIgaXRlbSA9IG5ld0NoYXJnZURhdGEuc3BsaWNlKGV2dC5vbGRJbmRleCwgMSlbMF07CiAgICAgICAgICAgICAgICAgIG5ld0NoYXJnZURhdGEuc3BsaWNlKGV2dC5uZXdJbmRleCwgMCwgaXRlbSk7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kZW1pdCgncmV0dXJuJywgbmV3Q2hhcmdlRGF0YSk7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kZW1pdCgnZHJhZ1VwZGF0ZScsIHsKICAgICAgICAgICAgICAgICAgICBpdGVtOiBpdGVtLAogICAgICAgICAgICAgICAgICAgIG9sZEluZGV4OiBldnQub2xkSW5kZXgsCiAgICAgICAgICAgICAgICAgICAgbmV3SW5kZXg6IGV2dC5uZXdJbmRleCwKICAgICAgICAgICAgICAgICAgICBjb2x1bW46IF90aGlzMi5kcmFnU3RhcnRDb2x1bW4KICAgICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgICAvLyDmi5bmi73mm7TmlrDlkI7liLfmlrDooajmoLwKICAgICAgICAgICAgICAgICAgX3RoaXMyLnJlZnJlc2hUYWJsZSgpOwogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIG9uRW5kOiBmdW5jdGlvbiBvbkVuZChldnQpIHsKICAgICAgICAgICAgICAgICAgLy8g5ouW5ou957uT5p2fCiAgICAgICAgICAgICAgICAgIF90aGlzMi4kZW1pdCgnZHJhZ0VuZCcsICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIGV2dCksIHt9LCB7CiAgICAgICAgICAgICAgICAgICAgZHJhZ0NvbHVtbjogX3RoaXMyLmRyYWdTdGFydENvbHVtbgogICAgICAgICAgICAgICAgICB9KSk7CiAgICAgICAgICAgICAgICAgIC8vIOmHjee9ruaLluaLveWIlwogICAgICAgICAgICAgICAgICBfdGhpczIuZHJhZ1N0YXJ0Q29sdW1uID0gLTE7CgogICAgICAgICAgICAgICAgICAvLyDmi5bmi73nu5PmnZ/lkI7liLfmlrDooajmoLwKICAgICAgICAgICAgICAgICAgX3RoaXMyLnJlZnJlc2hUYWJsZSgpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHNvcnRhYmxlOicsIGVycm9yKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgc2V0Um93RGF0YTogZnVuY3Rpb24gc2V0Um93RGF0YShfcmVmKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB2YXIgcm93ID0gX3JlZi5yb3csCiAgICAgICAgcm93SW5kZXggPSBfcmVmLnJvd0luZGV4OwogICAgICAvLyDkuLrmr4/ooYzorr7nva7mlbDmja7lsZ7mgKfvvIznlKjkuo7mi5bmi73kvKDpgJLmlbDmja4KICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIHZhciB0YWJsZVJvd3MgPSBfdGhpczMuJHJlZnMuY2hhcmdlVGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3JBbGwoJ3Rib2R5IHRyJyk7CiAgICAgICAgaWYgKHRhYmxlUm93c1tyb3dJbmRleF0pIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIHRhYmxlUm93c1tyb3dJbmRleF0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScsIEpTT04uc3RyaW5naWZ5KHJvdykpOwogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0cmluZ2lmeSByb3cgZGF0YTonLCBlcnJvcik7CiAgICAgICAgICAgIHRhYmxlUm93c1tyb3dJbmRleF0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScsICd7fScpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiAnJzsKICAgIH0sCiAgICBhdWRpdFN0YXR1czogZnVuY3Rpb24gYXVkaXRTdGF0dXMoc3RhdHVzKSB7CiAgICAgIHJldHVybiBzdGF0dXMgPT0gMSA/ICLlt7LlrqHmoLgiIDogIuacquWuoeaguCI7CiAgICB9LAogICAgc2VsZWN0Q2hhcmdlOiBmdW5jdGlvbiBzZWxlY3RDaGFyZ2UodGFyZ2V0LCByb3cpIHsKICAgICAgcm93LmRuQ2hhcmdlTmFtZUlkID0gdGFyZ2V0LmNoYXJnZUlkOwogICAgICByb3cuY2hhcmdlTmFtZSA9IHRhcmdldC5jaGFyZ2VMb2NhbE5hbWU7CiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICAvLyDlpITnkIbpgInmi6nlj5jljJYKICAgICAgdGhpcy4kZW1pdCgic2VsZWN0Um93IiwgdmFsKTsKICAgICAgdGhpcy5wYXlUb3RhbFVTRCA9IDA7CiAgICAgIHRoaXMucGF5VG90YWxSTUIgPSAwOwogICAgICB2YWwgPyB2YWwubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0uaXNSZWNpZXZpbmdPclBheWluZyA9PSAxKSB7CiAgICAgICAgICBpZiAoaXRlbS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsKICAgICAgICAgICAgX3RoaXM0LnBheVRvdGFsVVNEID0gKDAsIF9jdXJyZW5jeS5kZWZhdWx0KShfdGhpczQucGF5VG90YWxVU0QpLmFkZChpdGVtLnN1YnRvdGFsKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzNC5wYXlUb3RhbFJNQiA9ICgwLCBfY3VycmVuY3kuZGVmYXVsdCkoX3RoaXM0LnBheVRvdGFsUk1CKS5hZGQoaXRlbS5zdWJ0b3RhbCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KSA6IG51bGw7CiAgICB9LAogICAgY3VycmVuY3k6IF9jdXJyZW5jeS5kZWZhdWx0LAogICAgLy8g6I635Y+W6aG555uu55qE5ZSv5LiA6ZSuCiAgICBnZXRJdGVtS2V5OiBmdW5jdGlvbiBnZXRJdGVtS2V5KGl0ZW0pIHsKICAgICAgcmV0dXJuIGl0ZW0udGVtcElkIHx8IGl0ZW0uY2hhcmdlSWQgfHwgaXRlbS5pZCB8fCAiaXRlbV8iLmNvbmNhdChNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSkpOwogICAgfSwKICAgIGdldFNlcnZpY2VOYW1lOiBmdW5jdGlvbiBnZXRTZXJ2aWNlTmFtZShpZCkgewogICAgICB2YXIgc2VydmljZU5hbWUgPSAiIjsKICAgICAgdGhpcy5zZXJ2aWNlcy5tYXAoZnVuY3Rpb24gKG9iaikgewogICAgICAgIG9iai52YWx1ZSA9PT0gaWQgPyBzZXJ2aWNlTmFtZSA9IG9iai5sYWJlbCA6IG51bGw7CiAgICAgIH0pOwogICAgICByZXR1cm4gc2VydmljZU5hbWU7CiAgICB9LAogICAgY29weUZyZWlnaHQ6IGZ1bmN0aW9uIGNvcHlGcmVpZ2h0KHJvdykgewogICAgICBpZiAodGhpcy5jb21wYW55TGlzdC5sZW5ndGggPiAwKSB7CiAgICAgICAgcm93LnBheUNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5jb21wYW55TGlzdFswXS5jb21wYW55SWQ7CiAgICAgICAgcm93LnBheUNvbXBhbnlOYW1lID0gdGhpcy5jb21wYW55TGlzdFswXS5jb21wYW55U2hvcnROYW1lOwogICAgICB9CiAgICAgIHJvdy5pc0FjY291bnRDb25maXJtZWQgPSAwOwogICAgICAvLyDmiqXku7fliJfooajot7PovazorqLoiLHml7bmsqHmnInlhazlj7jliJfooags5aSN5Yi25Yiw5bqU5pS25rKh5pyJ5a6i5oi35L+h5oGvCiAgICAgIHZhciBkYXRhID0gdGhpcy5fLmNsb25lRGVlcChyb3cpOwogICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIGRhdGEpLCB7fSwgewogICAgICAgIGNoYXJnZUlkOiBudWxsCiAgICAgIH0pKTsKICAgIH0sCiAgICBjb3B5QWxsRnJlaWdodDogZnVuY3Rpb24gY29weUFsbEZyZWlnaHQoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBpZiAoIXRoaXMuY29tcGFueUxpc3QubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLmFsZXJ0V2FybmluZygi6K+35YWI6YCJ5oup5aeU5omY5Y2V5L2N5oiW5YWz6IGU5Y2V5L2NIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICghdGhpcy5jaGFyZ2VEYXRhIHx8ICF0aGlzLmNoYXJnZURhdGEubGVuZ3RoKSB7CiAgICAgICAgdGhpcy4kbW9kYWwuYWxlcnRXYXJuaW5nKCLmmoLml6DotLnnlKjmlbDmja7lj6/lpI3liLYiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5jaGFyZ2VEYXRhLm1hcChmdW5jdGlvbiAoY2hhcmdlKSB7CiAgICAgICAgY2hhcmdlLnBheUNsZWFyaW5nQ29tcGFueUlkID0gX3RoaXM1LmNvbXBhbnlMaXN0WzBdLmNvbXBhbnlJZDsKICAgICAgICBjaGFyZ2UucGF5Q29tcGFueU5hbWUgPSBfdGhpczUuY29tcGFueUxpc3RbMF0uY29tcGFueVNob3J0TmFtZTsKICAgICAgICBjaGFyZ2UuaXNSZWNpZXZpbmdPclBheWluZyA9IDA7CiAgICAgICAgY2hhcmdlLmlzQWNjb3VudENvbmZpcm1lZCA9IDA7CiAgICAgICAgY2hhcmdlLmNoYXJnZUlkID0gbnVsbDsKICAgICAgICBfdGhpczUuJGVtaXQoImNvcHlGcmVpZ2h0IiwgX3RoaXM1Ll8uY2xvbmVEZWVwKGNoYXJnZSkpOwogICAgICB9KTsKICAgIH0sCiAgICBjaGFuZ2VVbml0Q29zdDogZnVuY3Rpb24gY2hhbmdlVW5pdENvc3Qocm93LCB1bml0KSB7CiAgICAgIHJvdy5kblVuaXRDb2RlID0gdW5pdDsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIHJvdy5zaG93Q29zdFVuaXQgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgY2hhbmdlVW5pdDogZnVuY3Rpb24gY2hhbmdlVW5pdChyb3csIHVuaXQpIHsKICAgICAgcm93LmRuVW5pdENvZGUgPSB1bml0OwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgcm93LnNob3dRdW90YXRpb25Vbml0ID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUNoYXJnZVNlbGVjdDogZnVuY3Rpb24gaGFuZGxlQ2hhcmdlU2VsZWN0KHJvdywgZGF0YSkgewogICAgICBpZiAocm93LmNoYXJnZUxvY2FsTmFtZSA9PT0gZGF0YS5jaGFyZ2VOYW1lKSB7CiAgICAgICAgcm93LmNoYXJnZU5hbWUgPSBkYXRhLmNoYXJnZUxvY2FsTmFtZTsKICAgICAgICByb3cuc2hvd1F1b3RhdGlvbkNoYXJnZSA9IGZhbHNlOwogICAgICB9CiAgICAgIGlmIChyb3cuY3VycmVuY3lDb2RlID09IG51bGwgJiYgZGF0YS5jdXJyZW5jeUNvZGUpIHsKICAgICAgICByb3cuZG5DdXJyZW5jeUNvZGUgPSBkYXRhLmN1cnJlbmN5Q29kZTsKICAgICAgfQogICAgfSwKICAgIGNoYW5nZUN1cnJlbmN5OiBmdW5jdGlvbiBjaGFuZ2VDdXJyZW5jeShyb3csIGN1cnJlbmN5Q29kZSkgewogICAgICByb3cuZG5DdXJyZW5jeUNvZGUgPSBjdXJyZW5jeUNvZGU7CiAgICAgIC8qIGxldCBleGNoYW5nZVJhdGUNCiAgICAgIGlmIChjdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmV4Y2hhbmdlUmF0ZUxpc3QpIHsNCiAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgJiYgcm93LmRuQ3VycmVuY3lDb2RlID09IGEub3ZlcnNlYUN1cnJlbmN5DQogICAgICAgICAgKSB7DQogICAgICAgICAgICBleGNoYW5nZVJhdGUgPSBjdXJyZW5jeShhLnNldHRsZVJhdGUpLmRpdmlkZShhLmJhc2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9ICovCgogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgLy8gcm93LmJhc2ljQ3VycmVuY3lSYXRlID0gZXhjaGFuZ2VSYXRlID8gZXhjaGFuZ2VSYXRlIDogMQogICAgICAgIHJvdy5zaG93UXVvdGF0aW9uQ3VycmVuY3kgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOW6j+WPtyAqL3Jvd0luZGV4OiBmdW5jdGlvbiByb3dJbmRleChfcmVmMikgewogICAgICB2YXIgcm93ID0gX3JlZjIucm93LAogICAgICAgIHJvd0luZGV4ID0gX3JlZjIucm93SW5kZXg7CiAgICAgIHJvdy5pZCA9IHJvd0luZGV4ICsgMTsKICAgIH0sCiAgICBhZGRSZWNlaXZhYmxlUGF5YWJsZTogZnVuY3Rpb24gYWRkUmVjZWl2YWJsZVBheWFibGUoKSB7CiAgICAgIC8vIOehruS/nSBjaGFyZ2VEYXRhIOWtmOWcqAogICAgICBpZiAoIXRoaXMuY2hhcmdlRGF0YSkgewogICAgICAgIHRoaXMuJGVtaXQoInJldHVybiIsIFtdKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdmFyIG9iaiA9IHsKICAgICAgICBzaG93Q2xpZW50OiB0cnVlLAogICAgICAgIHNob3dTdXBwbGllcjogdHJ1ZSwKICAgICAgICBzaG93UXVvdGF0aW9uQ2hhcmdlOiB0cnVlLAogICAgICAgIHNob3dDb3N0Q2hhcmdlOiB0cnVlLAogICAgICAgIHNob3dRdW90YXRpb25DdXJyZW5jeTogdHJ1ZSwKICAgICAgICBzaG93Q29zdEN1cnJlbmN5OiB0cnVlLAogICAgICAgIHNob3dRdW90YXRpb25Vbml0OiB0cnVlLAogICAgICAgIHNob3dDb3N0VW5pdDogdHJ1ZSwKICAgICAgICBzaG93U3RyYXRlZ3k6IHRydWUsCiAgICAgICAgc2hvd1VuaXRSYXRlOiB0cnVlLAogICAgICAgIHNob3dBbW91bnQ6IHRydWUsCiAgICAgICAgc2hvd0N1cnJlbmN5UmF0ZTogdHJ1ZSwKICAgICAgICBzaG93RHV0eVJhdGU6IHRydWUsCiAgICAgICAgYmFzaWNDdXJyZW5jeVJhdGU6IDEsCiAgICAgICAgZHV0eVJhdGU6IDAsCiAgICAgICAgZG5BbW91bnQ6IDEsCiAgICAgICAgLy8g5bqU5pS26L+Y5piv5bqU5LuYCiAgICAgICAgaXNSZWNpZXZpbmdPclBheWluZzogdGhpcy5pc1JlY2VpdmFibGUgPyAwIDogMSwKICAgICAgICBjbGVhcmluZ0NvbXBhbnlJZDogdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCA+IDAgPyB0aGlzLmNoYXJnZURhdGFbdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCAtIDFdLmNsZWFyaW5nQ29tcGFueUlkIDogbnVsbAogICAgICB9OwogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSAxKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDE7CiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDEwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDEwOwogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSAyMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAyMDsKICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gNDApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gNDA7CiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDUwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDUwOwogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA2MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA2MDsKICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gNzApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gNzA7CiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDgwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDgwOwogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA5MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA5MDsKICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMTAwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDEwMDsKICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMTAxKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDEwMTsKICAgICAgdGhpcy5jaGFyZ2VEYXRhLnB1c2gob2JqKTsKCiAgICAgIC8vIOa3u+WKoOaWsOmhueebruWQjuWIt+aWsOihqOagvAogICAgICB0aGlzLnJlZnJlc2hUYWJsZSgpOwogICAgfSwKICAgIGNvdW50UHJvZml0OiBmdW5jdGlvbiBjb3VudFByb2ZpdChyb3csIGNhdGVnb3J5KSB7CiAgICAgIC8vIOehruS/neaJgOacieW/heimgeeahOWAvOmDveWtmOWcqOS4lOacieaViAogICAgICBpZiAoIXJvdykgcmV0dXJuOwoKICAgICAgLy8g5L2/55SoY3VycmVuY3kuanPmnaXlpITnkIbmlbDlgLws6YG/5YWN57K+5bqm5o2f5aSxCiAgICAgIHZhciB1bml0UmF0ZSA9IHJvdy5kblVuaXRSYXRlIHx8IDA7CiAgICAgIHZhciBhbW91bnQgPSByb3cuZG5BbW91bnQgfHwgMDsKICAgICAgdmFyIGN1cnJlbmN5UmF0ZSA9ICgwLCBfY3VycmVuY3kuZGVmYXVsdCkocm93LmJhc2ljQ3VycmVuY3lSYXRlIHx8IDEsIHsKICAgICAgICBwcmVjaXNpb246IDQKICAgICAgfSkudmFsdWU7CiAgICAgIHZhciBkdXR5UmF0ZSA9ICgwLCBfY3VycmVuY3kuZGVmYXVsdCkocm93LmR1dHlSYXRlIHx8IDApLnZhbHVlOwogICAgICB0cnkgewogICAgICAgIC8vIOiuoeeul+Wwj+iuoQogICAgICAgIHZhciBzdWJ0b3RhbCA9ICgwLCBfY3VycmVuY3kuZGVmYXVsdCkodW5pdFJhdGUsIHsKICAgICAgICAgIHByZWNpc2lvbjogNAogICAgICAgIH0pLm11bHRpcGx5KGFtb3VudCkubXVsdGlwbHkoY3VycmVuY3lSYXRlKS5tdWx0aXBseSgoMCwgX2N1cnJlbmN5LmRlZmF1bHQpKDEpLmFkZCgoMCwgX2N1cnJlbmN5LmRlZmF1bHQpKGR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpLnZhbHVlOwoKICAgICAgICAvLyDmm7TmlrDooYzmlbDmja4KICAgICAgICByb3cuc3VidG90YWwgPSAoMCwgX2N1cnJlbmN5LmRlZmF1bHQpKHN1YnRvdGFsLCB7CiAgICAgICAgICBwcmVjaXNpb246IDIKICAgICAgICB9KS52YWx1ZTsKICAgICAgICByb3cuc3FkRG5DdXJyZW5jeUJhbGFuY2UgPSByb3cuaXNBY2NvdW50Q29uZmlybWVkID09PSAiMCIgPyAoMCwgX2N1cnJlbmN5LmRlZmF1bHQpKHN1YnRvdGFsLCB7CiAgICAgICAgICBwcmVjaXNpb246IDIKICAgICAgICB9KS52YWx1ZSA6IHJvdy5zcWREbkN1cnJlbmN5QmFsYW5jZTsKCiAgICAgICAgLy8g5qC55o2u5LiN5ZCM55qE6L6T5YWl57G75Z6L5YWz6Zet5a+55bqU55qE57yW6L6R54q25oCBCiAgICAgICAgc3dpdGNoIChjYXRlZ29yeSkgewogICAgICAgICAgY2FzZSAic3RyYXRlZ3kiOgogICAgICAgICAgICByb3cuc2hvd1N0cmF0ZWd5ID0gZmFsc2U7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAidW5pdFJhdGUiOgogICAgICAgICAgICAvLyDkuI3lnKjov5nph4zlhbPpl63nvJbovpHnirbmgIEs5pS555SoQGJsdXLkuovku7YKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICJhbW91bnQiOgogICAgICAgICAgICAvLyDkuI3lnKjov5nph4zlhbPpl63nvJbovpHnirbmgIEs5pS555SoQGJsdXLkuovku7YKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICJjdXJyZW5jeVJhdGUiOgogICAgICAgICAgICAvLyDkuI3lnKjov5nph4zlhbPpl63nvJbovpHnirbmgIEs5pS555SoQGJsdXLkuovku7YKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICJkdXR5UmF0ZSI6CiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tgogICAgICAgICAgICBicmVhazsKICAgICAgICB9CgogICAgICAgIC8vIOinpuWPkeaVsOaNruabtOaWsAogICAgICAgIHRoaXMuJGVtaXQoInJldHVybiIsIHRoaXMuY2hhcmdlRGF0YSB8fCBbXSk7CgogICAgICAgIC8vIOaVsOaNruabtOaWsOWQjuWIt+aWsOihqOagvAogICAgICAgIHRoaXMucmVmcmVzaFRhYmxlKCk7CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigi6K6h566X5bCP6K6h5pe25Ye66ZSZOiIsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLorqHnrpflsI/orqHml7blh7rplJks6K+35qOA5p+l6L6T5YWl5YC85piv5ZCm5q2j56GuIik7CiAgICAgIH0KICAgIH0sCiAgICBkZWxldGVJdGVtOiBmdW5jdGlvbiBkZWxldGVJdGVtKHJvdykgewogICAgICB0aGlzLiRlbWl0KCJkZWxldGVJdGVtIiwgcm93KTsKICAgIH0sCiAgICBkZWxldGVBbGxJdGVtOiBmdW5jdGlvbiBkZWxldGVBbGxJdGVtKHJvdykgewogICAgICB0aGlzLiRlbWl0KCJkZWxldGVBbGwiKTsKICAgIH0sCiAgICBjb21wYW55Tm9ybWFsaXplcjogZnVuY3Rpb24gY29tcGFueU5vcm1hbGl6ZXIobm9kZSkgewogICAgICByZXR1cm4gewogICAgICAgIGlkOiBub2RlLmNvbXBhbnlJZCwKICAgICAgICBsYWJlbDogKG5vZGUuY29tcGFueVNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55U2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55TG9jYWxOYW1lIDogIiIpICsgIiwiICsgX2pzUGlueWluLmRlZmF1bHQuZ2V0RnVsbENoYXJzKChub2RlLmNvbXBhbnlTaG9ydE5hbWUgIT0gbnVsbCA/IG5vZGUuY29tcGFueVNob3J0TmFtZSA6ICIiKSArICIgIiArIChub2RlLmNvbXBhbnlMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY29tcGFueUxvY2FsTmFtZSA6ICIiKSkKICAgICAgfTsKICAgIH0sCiAgICAvLyDliLfmlrDooajmoLzluIPlsYAKICAgIHJlZnJlc2hUYWJsZTogZnVuY3Rpb24gcmVmcmVzaFRhYmxlKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIGlmIChfdGhpczYuJHJlZnMuY2hhcmdlVGFibGUpIHsKICAgICAgICAgIF90aGlzNi4kcmVmcy5jaGFyZ2VUYWJsZS5kb0xheW91dCgpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["_currency", "_interopRequireDefault", "require", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_index", "_rich", "_sortablejs", "name", "components", "CompanySelect", "Treeselect", "props", "computed", "hasConfirmRow", "result", "chargeData", "length", "map", "item", "isAccountConfirmed", "watch", "handler", "newVal", "oldVal", "$emit", "for<PERSON>ach", "index", "oldItem", "currency", "amount", "exchangeRate", "inverseRate", "precision", "divide", "value", "subtotal", "dnUnitRate", "multiply", "add", "dutyRate", "error", "console", "reinitializeSortable", "deep", "immediate", "disabled", "mounted", "initSortable", "<PERSON><PERSON><PERSON><PERSON>", "reinitTimer", "clearTimeout", "destroySortable", "data", "payTotalRMB", "payTotalUSD", "showClientName", "sortable", "dragStartColumn", "services", "label", "service", "chargeRemark", "methods", "el", "parentNode", "destroy", "warn", "_this", "setTimeout", "$nextTick", "_this2", "$refs", "chargeTable", "$el", "tbody", "querySelector", "Sortable", "create", "group", "dragGroupName", "pull", "to", "from", "dragEl", "evt", "put", "animation", "ghostClass", "dragClass", "filter", "onStart", "draggedItem", "oldIndex", "mouseX", "originalEvent", "clientX", "mouseY", "clientY", "cells", "querySelectorAll", "i", "rect", "getBoundingClientRect", "left", "right", "top", "bottom", "headerCells", "setAttribute", "JSON", "stringify", "column", "onAdd", "dragData", "getAttribute", "parse", "columnData", "parseInt", "newChargeData", "_toConsumableArray2", "default", "splice", "newIndex", "_objectSpread2", "tempId", "concat", "Date", "now", "Math", "random", "toString", "substr", "chargeId", "refreshTable", "onRemove", "removedItem", "isCut", "onUpdate", "onEnd", "dragColumn", "setRowData", "_ref", "_this3", "row", "rowIndex", "tableRows", "auditStatus", "status", "selectCharge", "target", "dnChargeNameId", "chargeName", "chargeLocalName", "handleSelectionChange", "val", "_this4", "isRecievingOrPaying", "dnCurrencyCode", "getItemKey", "id", "getServiceName", "serviceName", "obj", "copyFreight", "companyList", "payClearingCompanyId", "companyId", "payCompanyName", "companyShortName", "_", "cloneDeep", "copyAllFreight", "_this5", "$modal", "alertWarning", "charge", "changeUnitCost", "unit", "dnUnitCode", "showCostUnit", "changeUnit", "showQuotationUnit", "handleChargeSelect", "showQuotationCharge", "currencyCode", "changeCurrency", "showQuotationCurrency", "_ref2", "addReceivablePayable", "showClient", "showSupplier", "showCostCharge", "showCostCurrency", "showStrategy", "showUnitRate", "showAmount", "showCurrencyRate", "showDutyRate", "basicCurrencyRate", "dnAmount", "isReceivable", "clearingCompanyId", "serviceTypeId", "sqdServiceTypeId", "push", "countProfit", "category", "unitRate", "currencyRate", "sqdDnCurrencyBalance", "$message", "deleteItem", "deleteAllItem", "companyNormalizer", "node", "companyLocalName", "pinyin", "getFullChars", "_this6", "doLayout", "exports", "_default"], "sources": ["src/views/system/document/debitNoteChargeList.vue"], "sourcesContent": ["<template>\r\n  <el-col :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table\r\n        ref=\"chargeTable\"\r\n        :data=\"chargeData\"\r\n        border\r\n        class=\"pd0\"\r\n        row-key=\"getItemKey\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        :row-class-name=\"setRowData\"\r\n      >\r\n        <el-table-column\r\n          align=\"center\"\r\n          type=\"selection\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCharge\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n            >\r\n              {{ scope.row.chargeName }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\"\r\n                         :type=\"'charge'\"\r\n                         @return=\"scope.row.dnChargeNameId = $event\"\r\n                         @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n            >\r\n              {{ scope.row.dnCurrencyCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCurrency\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                         :pass=\"scope.row.dnCurrencyCode\" :type=\"'currency'\"\r\n                         @return=\"changeCurrency(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showUnitRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n            >\r\n              {{\r\n                scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"¥\"\r\n                }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"$\"\r\n                }).format() : scope.row.dnUnitRate)\r\n              }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                             :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\"\r\n                             :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                             @blur=\"scope.row.showUnitRate=false\"\r\n                             @change=\"countProfit(scope.row,'unitRate')\"\r\n                             @input=\"countProfit(scope.row,'unitRate')\"\r\n                             @focusout.native=\"scope.row.showUnitRate=false\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationUnit\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n            >\r\n              {{ scope.row.dnUnitCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                         :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                         :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showAmount\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n            >\r\n              {{ scope.row.dnAmount }}\r\n            </div>\r\n            <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.00\" placeholder=\"数量\"\r\n                             style=\"display:flex;width: 100%\" @blur=\"scope.row.showAmount=false\"\r\n                             @change=\"countProfit(scope.row,'amount')\"\r\n                             @input=\"countProfit(scope.row,'amount')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n          <template slot-scope=\"scope\" style=\"display:flex;\">\r\n            <div v-if=\"!scope.row.showCurrencyRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n            >\r\n              {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                             :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\" :precision=\"4\" :step=\"0.0001\"\r\n                             style=\"width: 100%\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                             @change=\"countProfit(scope.row,'currencyRate')\"\r\n                             @input=\"countProfit(scope.row,'currencyRate')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"display: flex;justify-content: center\">\r\n              <div v-if=\"!scope.row.showDutyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n              >\r\n                {{ scope.row.dutyRate }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :min=\"0\" style=\"width: 75%\"\r\n                               @blur=\"scope.row.showDutyRate=false\"\r\n                               @change=\"countProfit(scope.row,'dutyRate')\"\r\n                               @input=\"countProfit(scope.row,'dutyRate')\"\r\n              />\r\n              <div>%</div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{\r\n                currency(scope.row.subtotal, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                }).format()\r\n              }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用备注\">\r\n          <template slot-scope=\"scope\">\r\n            <input v-model=\"scope.row.chargeRemark\"\r\n                   :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                   style=\"border: none;width: 100%;height: 100%;\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"审核状态\">\r\n          <template slot-scope=\"scope\">\r\n            {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"已收金额\">\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.sqdDnCurrencyPaid\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"未收余额\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属服务\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              style=\"color: red\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <!-- 空数据提示 -->\r\n        <template slot=\"empty\">\r\n          <div style=\"padding: 20px; text-align: center; color: #909399; display: flex; flex-direction: column; align-items: center;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 24px; margin-bottom: 10px;\"></i>\r\n            <p>暂无费用数据</p>\r\n          </div>\r\n        </template>\r\n      </el-table>\r\n    </div>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport Sortable from \"sortablejs\"\r\n\r\nexport default {\r\n  name: \"debitNoteChargeList\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\", \"debitNote\", \"dragGroupName\"],\r\n  computed: {\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n\r\n        // 数据变化后重新初始化拖拽，使用更安全的方式\r\n        this.reinitializeSortable()\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    },\r\n    disabled: {\r\n      handler: function (newVal) {\r\n        // 禁用状态改变时重新初始化拖拽\r\n        this.reinitializeSortable()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initSortable()\r\n  },\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.reinitTimer) {\r\n      clearTimeout(this.reinitTimer)\r\n      this.reinitTimer = null\r\n    }\r\n    // 销毁拖拽实例\r\n    this.destroySortable()\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      sortable: null,\r\n      reinitTimer: null,\r\n      dragStartColumn: -1, // 记录拖拽开始的列索引\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null\r\n    }\r\n  },\r\n  methods: {\r\n    destroySortable() {\r\n      if (this.sortable) {\r\n        try {\r\n          // 检查 sortable 实例是否还有效\r\n          if (this.sortable.el && this.sortable.el.parentNode) {\r\n            this.sortable.destroy()\r\n          }\r\n        } catch (error) {\r\n          console.warn('Error destroying sortable:', error)\r\n        } finally {\r\n          this.sortable = null\r\n        }\r\n      }\r\n    },\r\n    reinitializeSortable() {\r\n      // 使用防抖延迟重新初始化，避免频繁的创建销毁\r\n      if (this.reinitTimer) {\r\n        clearTimeout(this.reinitTimer)\r\n      }\r\n\r\n      this.reinitTimer = setTimeout(() => {\r\n        this.$nextTick(() => {\r\n          this.destroySortable()\r\n          // 确保 DOM 已更新后再初始化\r\n          this.$nextTick(() => {\r\n            this.initSortable()\r\n          })\r\n        })\r\n      }, 100)\r\n    },\r\n    initSortable() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.chargeTable && this.$refs.chargeTable.$el) {\r\n          const tbody = this.$refs.chargeTable.$el.querySelector('tbody')\r\n          if (tbody) {\r\n            try {\r\n              this.sortable = Sortable.create(tbody, {\r\n                group: {\r\n                  name: this.dragGroupName || 'debitNoteChargeGroup',\r\n                  pull: (to, from, dragEl, evt) => {\r\n                    // 根据拖拽列决定是否允许拖出（剪切或复制）\r\n                    if (this.dragStartColumn === 1) {\r\n                      return true; // 从第一列拖动时允许拖出（剪切）\r\n                    } else {\r\n                      return 'clone'; // 从其他列拖动时复制\r\n                    }\r\n                  },\r\n                  put: !this.disabled\r\n                },\r\n                animation: 150,\r\n                disabled: this.disabled,\r\n                ghostClass: 'sortable-ghost',\r\n                dragClass: 'sortable-drag',\r\n                filter: '.disabled',\r\n                onStart: (evt) => {\r\n                  // 拖拽开始\r\n                  if (!this.chargeData || !this.chargeData.length) return\r\n                  const draggedItem = this.chargeData[evt.oldIndex]\r\n\r\n                  // 获取拖拽开始的列索引\r\n                  this.dragStartColumn = -1 // 默认设置为-1\r\n                  try {\r\n                    // 获取拖拽事件的起始坐标\r\n                    const mouseX = evt.originalEvent.clientX\r\n                    const mouseY = evt.originalEvent.clientY\r\n\r\n                    // 尝试更准确地确定拖拽开始的列\r\n                    const cells = evt.item.querySelectorAll('td')\r\n                    if (cells && cells.length > 0) {\r\n                      // 计算每个单元格的位置，找到包含鼠标位置的单元格\r\n                      for (let i = 0; i < cells.length; i++) {\r\n                        const rect = cells[i].getBoundingClientRect()\r\n                        if (mouseX >= rect.left && mouseX <= rect.right &&\r\n                          mouseY >= rect.top && mouseY <= rect.bottom) {\r\n                          this.dragStartColumn = i\r\n                          break\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    // 备选方法：如果上面的方法没找到，使用表头定位\r\n                    if (this.dragStartColumn === -1) {\r\n                      const headerCells = this.$refs.chargeTable.$el.querySelectorAll('thead th')\r\n                      if (headerCells && headerCells.length > 0) {\r\n                        for (let i = 0; i < headerCells.length; i++) {\r\n                          const rect = headerCells[i].getBoundingClientRect()\r\n                          if (mouseX >= rect.left && mouseX <= rect.right) {\r\n                            this.dragStartColumn = i\r\n                            break\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    if (this.dragStartColumn === -1) {\r\n                      // 回退方案：如果通过坐标无法确定，则默认为非第一列\r\n                      this.dragStartColumn = 2 // 设置为非第一列，默认为复制模式\r\n                    }\r\n\r\n                  } catch (error) {\r\n                    console.error('确定拖拽开始列时出错:', error)\r\n                    this.dragStartColumn = 1 // 出错时默认为第一列\r\n                  }\r\n\r\n                  // 设置被拖拽元素的数据\r\n                  try {\r\n                    evt.item.setAttribute('data-drag-item', JSON.stringify(draggedItem))\r\n                    // 额外添加拖拽起始列信息\r\n                    evt.item.setAttribute('data-drag-column', this.dragStartColumn)\r\n                  } catch (error) {\r\n                    console.error('Failed to stringify drag item:', error)\r\n                    evt.item.setAttribute('data-drag-item', '{}')\r\n                    evt.item.setAttribute('data-drag-column', '-1')\r\n                  }\r\n\r\n                  this.$emit('dragStart', {\r\n                    item: draggedItem,\r\n                    index: evt.oldIndex,\r\n                    from: this,\r\n                    column: this.dragStartColumn\r\n                  })\r\n                },\r\n                onAdd: (evt) => {\r\n                  // 接收到新元素\r\n                  if (!this.chargeData) return\r\n                  const item = evt.item\r\n                  let draggedItem = {}\r\n                  let dragStartColumn = -1\r\n\r\n                  try {\r\n                    const dragData = item.getAttribute('data-drag-item')\r\n                    if (dragData && dragData !== 'undefined') {\r\n                      draggedItem = JSON.parse(dragData)\r\n                    }\r\n\r\n                    // 获取拖拽起始列\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag item data:', error)\r\n                  }\r\n\r\n                  // 处理新增元素到表格\r\n                  const newChargeData = [...this.chargeData]\r\n\r\n                  // 无论是复制还是剪切，都需要添加项到目标位置\r\n                  // 但要给新项生成一个新的ID，表示这是一个全新的项\r\n                  newChargeData.splice(evt.newIndex, 0, {\r\n                    ...draggedItem,\r\n                    tempId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n                    chargeId: null // 清除原有ID，作为新增项\r\n                  })\r\n\r\n                  this.$emit('return', newChargeData)\r\n                                     this.$emit('dragAdd', {\r\n                     item: draggedItem,\r\n                     newIndex: evt.newIndex,\r\n                     to: this,\r\n                     column: dragStartColumn\r\n                   })\r\n                   \r\n                   // 拖拽添加后刷新表格\r\n                   this.refreshTable()\r\n                },\r\n                onRemove: (evt) => {\r\n                  // 获取拖拽信息\r\n                  if (!this.chargeData) return\r\n                  const item = evt.item\r\n                  let dragStartColumn = -1\r\n                  try {\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag column data:', error)\r\n                  }\r\n\r\n                  const newChargeData = [...this.chargeData]\r\n                  const removedItem = newChargeData[evt.oldIndex]\r\n\r\n                  // 只有在从第一列开始拖拽时才执行剪切操作\r\n                  // Sortable的clone选项已经控制了复制行为，这里我们只需处理剪切的情况\r\n                  if (dragStartColumn === 1) {\r\n                    newChargeData.splice(evt.oldIndex, 1)\r\n                    this.$emit('return', newChargeData)\r\n                  }\r\n\r\n                                     this.$emit('dragRemove', {\r\n                     item: removedItem,\r\n                     oldIndex: evt.oldIndex,\r\n                     from: this,\r\n                     column: dragStartColumn,\r\n                     isCut: dragStartColumn === 1\r\n                   })\r\n                   \r\n                   // 拖拽移除后刷新表格\r\n                   this.refreshTable()\r\n                },\r\n                onUpdate: (evt) => {\r\n                  // 同一表格内排序\r\n                  if (!this.chargeData) return\r\n                  const newChargeData = [...this.chargeData]\r\n                  const item = newChargeData.splice(evt.oldIndex, 1)[0]\r\n                  newChargeData.splice(evt.newIndex, 0, item)\r\n\r\n                  this.$emit('return', newChargeData)\r\n                                     this.$emit('dragUpdate', {\r\n                     item: item,\r\n                     oldIndex: evt.oldIndex,\r\n                     newIndex: evt.newIndex,\r\n                     column: this.dragStartColumn\r\n                   })\r\n                   \r\n                   // 拖拽更新后刷新表格\r\n                   this.refreshTable()\r\n                },\r\n                                 onEnd: (evt) => {\r\n                   // 拖拽结束\r\n                   this.$emit('dragEnd', {\r\n                     ...evt,\r\n                     dragColumn: this.dragStartColumn\r\n                   })\r\n                   // 重置拖拽列\r\n                   this.dragStartColumn = -1\r\n                   \r\n                   // 拖拽结束后刷新表格\r\n                   this.refreshTable()\r\n                 }\r\n              })\r\n            } catch (error) {\r\n              console.error('Error creating sortable:', error)\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    setRowData({row, rowIndex}) {\r\n      // 为每行设置数据属性，用于拖拽传递数据\r\n      this.$nextTick(() => {\r\n        const tableRows = this.$refs.chargeTable.$el.querySelectorAll('tbody tr')\r\n        if (tableRows[rowIndex]) {\r\n          try {\r\n            tableRows[rowIndex].setAttribute('data-drag-item', JSON.stringify(row))\r\n          } catch (error) {\r\n            console.error('Failed to stringify row data:', error)\r\n            tableRows[rowIndex].setAttribute('data-drag-item', '{}')\r\n          }\r\n        }\r\n      })\r\n      return ''\r\n    },\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      // 处理选择变化\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    // 获取项目的唯一键\r\n    getItemKey(item) {\r\n      return item.tempId || item.chargeId || item.id || `item_${Math.random().toString(36).substr(2, 9)}`\r\n    },\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      if (!this.chargeData || !this.chargeData.length) {\r\n        this.$modal.alertWarning(\"暂无费用数据可复制\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      // 确保 chargeData 存在\r\n      if (!this.chargeData) {\r\n        this.$emit(\"return\", [])\r\n        return\r\n      }\r\n\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n             if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n       this.chargeData.push(obj)\r\n       \r\n       // 添加新项目后刷新表格\r\n       this.refreshTable()\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n                 // 触发数据更新\r\n         this.$emit(\"return\", this.chargeData || [])\r\n         \r\n         // 数据更新后刷新表格\r\n         this.refreshTable()\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n         companyNormalizer(node) {\r\n       return {\r\n         id: node.companyId,\r\n         label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n       }\r\n     },\r\n     // 刷新表格布局\r\n     refreshTable() {\r\n       this.$nextTick(() => {\r\n         if (this.$refs.chargeTable) {\r\n           this.$refs.chargeTable.doLayout()\r\n         }\r\n       })\r\n     }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 拖拽样式\r\n::v-deep .sortable-ghost {\r\n  opacity: 0.5;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n::v-deep .sortable-drag {\r\n  opacity: 0.8;\r\n  background-color: #ecf5ff;\r\n  border: 1px dashed #409eff;\r\n}\r\n\r\n// 拖拽时的表格行样式\r\n::v-deep tbody tr {\r\n  cursor: move;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n::v-deep tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n// 禁用状态下不显示拖拽光标\r\n::v-deep tbody tr.disabled {\r\n  cursor: default;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAiOA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAN,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAM,IAAA;EACAC,UAAA;IAAAC,aAAA,EAAAA,cAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,KAAA,8EACA,qDACA,mGACA,6FACA,oGACA,8GACA;EACAC,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,MAAA;MACA,KAAAC,UAAA,SAAAA,UAAA,CAAAC,MAAA,YAAAD,UAAA,CAAAE,GAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,kBAAA;UACAL,MAAA;QACA;MACA;MACA,OAAAA,MAAA;IACA;EACA;EACAM,KAAA;IACAL,UAAA;MACAM,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,KAAAA,MAAA;UACA,KAAAC,KAAA,WAAAF,MAAA;UACA;QACA;;QAEA;QACAA,MAAA,GAAAA,MAAA,CAAAG,OAAA,WAAAP,IAAA,EAAAQ,KAAA;UACA,IAAAC,OAAA,GAAAJ,MAAA,CAAAG,KAAA;;UAEA;UACA,IAAAR,IAAA,CAAAU,QAAA,IAAAV,IAAA,CAAAW,MAAA;YACA;YACA,IAAAF,OAAA,IAAAA,OAAA,CAAAC,QAAA,cAAAV,IAAA,CAAAU,QAAA;cACA,IAAAV,IAAA,CAAAY,YAAA,IAAAZ,IAAA,CAAAY,YAAA;gBACA;kBACA;kBACA,IAAAC,WAAA,OAAAH,iBAAA;oBAAAI,SAAA;kBAAA,GAAAC,MAAA,CAAAf,IAAA,CAAAY,YAAA,EAAAI,KAAA;;kBAEA;kBACAhB,IAAA,CAAAiB,QAAA,OAAAP,iBAAA,EAAAV,IAAA,CAAAkB,UAAA;oBAAAJ,SAAA;kBAAA,GACAK,QAAA,CAAAnB,IAAA,CAAAW,MAAA,EACAQ,QAAA,CAAAN,WAAA,EACAM,QAAA,KAAAT,iBAAA,KAAAU,GAAA,KAAAV,iBAAA,EAAAV,IAAA,CAAAqB,QAAA,OAAAN,MAAA,QACAC,KAAA;gBACA,SAAAM,KAAA;kBACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;kBACAtB,IAAA,CAAAiB,QAAA;gBACA;cACA;YACA;UACA;QACA;QAEA,KAAAX,KAAA,WAAAF,MAAA,GAAAA,MAAA;;QAEA;QACA,KAAAoB,oBAAA;MACA;MACAC,IAAA;MACAC,SAAA;IACA;IACAC,QAAA;MACAxB,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,KAAAoB,oBAAA;MACA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAC,WAAA;MACAC,YAAA,MAAAD,WAAA;MACA,KAAAA,WAAA;IACA;IACA;IACA,KAAAE,eAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,QAAA;MACAP,WAAA;MACAQ,eAAA;MAAA;MACAC,QAAA;QACAxB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;MACAC,OAAA;QACA1B,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QACAzB,KAAA;QACAyB,KAAA;MACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,GACA;QAAAzB,KAAA;QAAAyB,KAAA;MAAA,EACA;MACAE,YAAA;IACA;EACA;EACAC,OAAA;IACAX,eAAA,WAAAA,gBAAA;MACA,SAAAK,QAAA;QACA;UACA;UACA,SAAAA,QAAA,CAAAO,EAAA,SAAAP,QAAA,CAAAO,EAAA,CAAAC,UAAA;YACA,KAAAR,QAAA,CAAAS,OAAA;UACA;QACA,SAAAzB,KAAA;UACAC,OAAA,CAAAyB,IAAA,+BAAA1B,KAAA;QACA;UACA,KAAAgB,QAAA;QACA;MACA;IACA;IACAd,oBAAA,WAAAA,qBAAA;MAAA,IAAAyB,KAAA;MACA;MACA,SAAAlB,WAAA;QACAC,YAAA,MAAAD,WAAA;MACA;MAEA,KAAAA,WAAA,GAAAmB,UAAA;QACAD,KAAA,CAAAE,SAAA;UACAF,KAAA,CAAAhB,eAAA;UACA;UACAgB,KAAA,CAAAE,SAAA;YACAF,KAAA,CAAApB,YAAA;UACA;QACA;MACA;IACA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAAuB,MAAA;MACA,KAAAD,SAAA;QACA,IAAAC,MAAA,CAAAC,KAAA,CAAAC,WAAA,IAAAF,MAAA,CAAAC,KAAA,CAAAC,WAAA,CAAAC,GAAA;UACA,IAAAC,KAAA,GAAAJ,MAAA,CAAAC,KAAA,CAAAC,WAAA,CAAAC,GAAA,CAAAE,aAAA;UACA,IAAAD,KAAA;YACA;cACAJ,MAAA,CAAAd,QAAA,GAAAoB,mBAAA,CAAAC,MAAA,CAAAH,KAAA;gBACAI,KAAA;kBACAvE,IAAA,EAAA+D,MAAA,CAAAS,aAAA;kBACAC,IAAA,WAAAA,KAAAC,EAAA,EAAAC,IAAA,EAAAC,MAAA,EAAAC,GAAA;oBACA;oBACA,IAAAd,MAAA,CAAAb,eAAA;sBACA;oBACA;sBACA;oBACA;kBACA;;kBACA4B,GAAA,GAAAf,MAAA,CAAAzB;gBACA;gBACAyC,SAAA;gBACAzC,QAAA,EAAAyB,MAAA,CAAAzB,QAAA;gBACA0C,UAAA;gBACAC,SAAA;gBACAC,MAAA;gBACAC,OAAA,WAAAA,QAAAN,GAAA;kBACA;kBACA,KAAAd,MAAA,CAAAvD,UAAA,KAAAuD,MAAA,CAAAvD,UAAA,CAAAC,MAAA;kBACA,IAAA2E,WAAA,GAAArB,MAAA,CAAAvD,UAAA,CAAAqE,GAAA,CAAAQ,QAAA;;kBAEA;kBACAtB,MAAA,CAAAb,eAAA;kBACA;oBACA;oBACA,IAAAoC,MAAA,GAAAT,GAAA,CAAAU,aAAA,CAAAC,OAAA;oBACA,IAAAC,MAAA,GAAAZ,GAAA,CAAAU,aAAA,CAAAG,OAAA;;oBAEA;oBACA,IAAAC,KAAA,GAAAd,GAAA,CAAAlE,IAAA,CAAAiF,gBAAA;oBACA,IAAAD,KAAA,IAAAA,KAAA,CAAAlF,MAAA;sBACA;sBACA,SAAAoF,CAAA,MAAAA,CAAA,GAAAF,KAAA,CAAAlF,MAAA,EAAAoF,CAAA;wBACA,IAAAC,IAAA,GAAAH,KAAA,CAAAE,CAAA,EAAAE,qBAAA;wBACA,IAAAT,MAAA,IAAAQ,IAAA,CAAAE,IAAA,IAAAV,MAAA,IAAAQ,IAAA,CAAAG,KAAA,IACAR,MAAA,IAAAK,IAAA,CAAAI,GAAA,IAAAT,MAAA,IAAAK,IAAA,CAAAK,MAAA;0BACApC,MAAA,CAAAb,eAAA,GAAA2C,CAAA;0BACA;wBACA;sBACA;oBACA;;oBAEA;oBACA,IAAA9B,MAAA,CAAAb,eAAA;sBACA,IAAAkD,WAAA,GAAArC,MAAA,CAAAC,KAAA,CAAAC,WAAA,CAAAC,GAAA,CAAA0B,gBAAA;sBACA,IAAAQ,WAAA,IAAAA,WAAA,CAAA3F,MAAA;wBACA,SAAAoF,EAAA,MAAAA,EAAA,GAAAO,WAAA,CAAA3F,MAAA,EAAAoF,EAAA;0BACA,IAAAC,KAAA,GAAAM,WAAA,CAAAP,EAAA,EAAAE,qBAAA;0BACA,IAAAT,MAAA,IAAAQ,KAAA,CAAAE,IAAA,IAAAV,MAAA,IAAAQ,KAAA,CAAAG,KAAA;4BACAlC,MAAA,CAAAb,eAAA,GAAA2C,EAAA;4BACA;0BACA;wBACA;sBACA;oBACA;oBAEA,IAAA9B,MAAA,CAAAb,eAAA;sBACA;sBACAa,MAAA,CAAAb,eAAA;oBACA;kBAEA,SAAAjB,KAAA;oBACAC,OAAA,CAAAD,KAAA,gBAAAA,KAAA;oBACA8B,MAAA,CAAAb,eAAA;kBACA;;kBAEA;kBACA;oBACA2B,GAAA,CAAAlE,IAAA,CAAA0F,YAAA,mBAAAC,IAAA,CAAAC,SAAA,CAAAnB,WAAA;oBACA;oBACAP,GAAA,CAAAlE,IAAA,CAAA0F,YAAA,qBAAAtC,MAAA,CAAAb,eAAA;kBACA,SAAAjB,KAAA;oBACAC,OAAA,CAAAD,KAAA,mCAAAA,KAAA;oBACA4C,GAAA,CAAAlE,IAAA,CAAA0F,YAAA;oBACAxB,GAAA,CAAAlE,IAAA,CAAA0F,YAAA;kBACA;kBAEAtC,MAAA,CAAA9C,KAAA;oBACAN,IAAA,EAAAyE,WAAA;oBACAjE,KAAA,EAAA0D,GAAA,CAAAQ,QAAA;oBACAV,IAAA,EAAAZ,MAAA;oBACAyC,MAAA,EAAAzC,MAAA,CAAAb;kBACA;gBACA;gBACAuD,KAAA,WAAAA,MAAA5B,GAAA;kBACA;kBACA,KAAAd,MAAA,CAAAvD,UAAA;kBACA,IAAAG,IAAA,GAAAkE,GAAA,CAAAlE,IAAA;kBACA,IAAAyE,WAAA;kBACA,IAAAlC,eAAA;kBAEA;oBACA,IAAAwD,QAAA,GAAA/F,IAAA,CAAAgG,YAAA;oBACA,IAAAD,QAAA,IAAAA,QAAA;sBACAtB,WAAA,GAAAkB,IAAA,CAAAM,KAAA,CAAAF,QAAA;oBACA;;oBAEA;oBACA,IAAAG,UAAA,GAAAlG,IAAA,CAAAgG,YAAA;oBACA,IAAAE,UAAA,IAAAA,UAAA;sBACA3D,eAAA,GAAA4D,QAAA,CAAAD,UAAA;oBACA;kBACA,SAAA5E,KAAA;oBACAC,OAAA,CAAAD,KAAA,oCAAAA,KAAA;kBACA;;kBAEA;kBACA,IAAA8E,aAAA,OAAAC,mBAAA,CAAAC,OAAA,EAAAlD,MAAA,CAAAvD,UAAA;;kBAEA;kBACA;kBACAuG,aAAA,CAAAG,MAAA,CAAArC,GAAA,CAAAsC,QAAA,SAAAC,cAAA,CAAAH,OAAA,MAAAG,cAAA,CAAAH,OAAA,MACA7B,WAAA;oBACAiC,MAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA,SAAAF,MAAA,CAAAG,IAAA,CAAAC,MAAA,GAAAC,QAAA,KAAAC,MAAA;oBACAC,QAAA;kBAAA,EACA;;kBAEA9D,MAAA,CAAA9C,KAAA,WAAA8F,aAAA;kBACAhD,MAAA,CAAA9C,KAAA;oBACAN,IAAA,EAAAyE,WAAA;oBACA+B,QAAA,EAAAtC,GAAA,CAAAsC,QAAA;oBACAzC,EAAA,EAAAX,MAAA;oBACAyC,MAAA,EAAAtD;kBACA;;kBAEA;kBACAa,MAAA,CAAA+D,YAAA;gBACA;gBACAC,QAAA,WAAAA,SAAAlD,GAAA;kBACA;kBACA,KAAAd,MAAA,CAAAvD,UAAA;kBACA,IAAAG,IAAA,GAAAkE,GAAA,CAAAlE,IAAA;kBACA,IAAAuC,eAAA;kBACA;oBACA,IAAA2D,UAAA,GAAAlG,IAAA,CAAAgG,YAAA;oBACA,IAAAE,UAAA,IAAAA,UAAA;sBACA3D,eAAA,GAAA4D,QAAA,CAAAD,UAAA;oBACA;kBACA,SAAA5E,KAAA;oBACAC,OAAA,CAAAD,KAAA,sCAAAA,KAAA;kBACA;kBAEA,IAAA8E,aAAA,OAAAC,mBAAA,CAAAC,OAAA,EAAAlD,MAAA,CAAAvD,UAAA;kBACA,IAAAwH,WAAA,GAAAjB,aAAA,CAAAlC,GAAA,CAAAQ,QAAA;;kBAEA;kBACA;kBACA,IAAAnC,eAAA;oBACA6D,aAAA,CAAAG,MAAA,CAAArC,GAAA,CAAAQ,QAAA;oBACAtB,MAAA,CAAA9C,KAAA,WAAA8F,aAAA;kBACA;kBAEAhD,MAAA,CAAA9C,KAAA;oBACAN,IAAA,EAAAqH,WAAA;oBACA3C,QAAA,EAAAR,GAAA,CAAAQ,QAAA;oBACAV,IAAA,EAAAZ,MAAA;oBACAyC,MAAA,EAAAtD,eAAA;oBACA+E,KAAA,EAAA/E,eAAA;kBACA;;kBAEA;kBACAa,MAAA,CAAA+D,YAAA;gBACA;gBACAI,QAAA,WAAAA,SAAArD,GAAA;kBACA;kBACA,KAAAd,MAAA,CAAAvD,UAAA;kBACA,IAAAuG,aAAA,OAAAC,mBAAA,CAAAC,OAAA,EAAAlD,MAAA,CAAAvD,UAAA;kBACA,IAAAG,IAAA,GAAAoG,aAAA,CAAAG,MAAA,CAAArC,GAAA,CAAAQ,QAAA;kBACA0B,aAAA,CAAAG,MAAA,CAAArC,GAAA,CAAAsC,QAAA,KAAAxG,IAAA;kBAEAoD,MAAA,CAAA9C,KAAA,WAAA8F,aAAA;kBACAhD,MAAA,CAAA9C,KAAA;oBACAN,IAAA,EAAAA,IAAA;oBACA0E,QAAA,EAAAR,GAAA,CAAAQ,QAAA;oBACA8B,QAAA,EAAAtC,GAAA,CAAAsC,QAAA;oBACAX,MAAA,EAAAzC,MAAA,CAAAb;kBACA;;kBAEA;kBACAa,MAAA,CAAA+D,YAAA;gBACA;gBACAK,KAAA,WAAAA,MAAAtD,GAAA;kBACA;kBACAd,MAAA,CAAA9C,KAAA,gBAAAmG,cAAA,CAAAH,OAAA,MAAAG,cAAA,CAAAH,OAAA,MACApC,GAAA;oBACAuD,UAAA,EAAArE,MAAA,CAAAb;kBAAA,EACA;kBACA;kBACAa,MAAA,CAAAb,eAAA;;kBAEA;kBACAa,MAAA,CAAA+D,YAAA;gBACA;cACA;YACA,SAAA7F,KAAA;cACAC,OAAA,CAAAD,KAAA,6BAAAA,KAAA;YACA;UACA;QACA;MACA;IACA;IACAoG,UAAA,WAAAA,WAAAC,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA;QAAAC,QAAA,GAAAH,IAAA,CAAAG,QAAA;MACA;MACA,KAAA3E,SAAA;QACA,IAAA4E,SAAA,GAAAH,MAAA,CAAAvE,KAAA,CAAAC,WAAA,CAAAC,GAAA,CAAA0B,gBAAA;QACA,IAAA8C,SAAA,CAAAD,QAAA;UACA;YACAC,SAAA,CAAAD,QAAA,EAAApC,YAAA,mBAAAC,IAAA,CAAAC,SAAA,CAAAiC,GAAA;UACA,SAAAvG,KAAA;YACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;YACAyG,SAAA,CAAAD,QAAA,EAAApC,YAAA;UACA;QACA;MACA;MACA;IACA;IACAsC,WAAA,WAAAA,YAAAC,MAAA;MACA,OAAAA,MAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,MAAA,EAAAN,GAAA;MACAA,GAAA,CAAAO,cAAA,GAAAD,MAAA,CAAAjB,QAAA;MACAW,GAAA,CAAAQ,UAAA,GAAAF,MAAA,CAAAG,eAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAnI,KAAA,cAAAkI,GAAA;MAEA,KAAApG,WAAA;MACA,KAAAD,WAAA;MACAqG,GAAA,GAAAA,GAAA,CAAAzI,GAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAA0I,mBAAA;UACA,IAAA1I,IAAA,CAAA2I,cAAA;YACAF,MAAA,CAAArG,WAAA,OAAA1B,iBAAA,EAAA+H,MAAA,CAAArG,WAAA,EAAAhB,GAAA,CAAApB,IAAA,CAAAiB,QAAA;UACA;YACAwH,MAAA,CAAAtG,WAAA,OAAAzB,iBAAA,EAAA+H,MAAA,CAAAtG,WAAA,EAAAf,GAAA,CAAApB,IAAA,CAAAiB,QAAA;UACA;QAEA;MACA;IAEA;IACAP,QAAA,EAAAA,iBAAA;IACA;IACAkI,UAAA,WAAAA,WAAA5I,IAAA;MACA,OAAAA,IAAA,CAAA0G,MAAA,IAAA1G,IAAA,CAAAkH,QAAA,IAAAlH,IAAA,CAAA6I,EAAA,YAAAlC,MAAA,CAAAG,IAAA,CAAAC,MAAA,GAAAC,QAAA,KAAAC,MAAA;IACA;IACA6B,cAAA,WAAAA,eAAAD,EAAA;MACA,IAAAE,WAAA;MACA,KAAAvG,QAAA,CAAAzC,GAAA,WAAAiJ,GAAA;QACAA,GAAA,CAAAhI,KAAA,KAAA6H,EAAA,GAAAE,WAAA,GAAAC,GAAA,CAAAvG,KAAA;MACA;MACA,OAAAsG,WAAA;IACA;IACAE,WAAA,WAAAA,YAAApB,GAAA;MACA,SAAAqB,WAAA,CAAApJ,MAAA;QACA+H,GAAA,CAAAsB,oBAAA,QAAAD,WAAA,IAAAE,SAAA;QACAvB,GAAA,CAAAwB,cAAA,QAAAH,WAAA,IAAAI,gBAAA;MACA;MACAzB,GAAA,CAAA5H,kBAAA;MACA;MACA,IAAAiC,IAAA,QAAAqH,CAAA,CAAAC,SAAA,CAAA3B,GAAA;MAEA,KAAAvH,KAAA,oBAAAmG,cAAA,CAAAH,OAAA,MAAAG,cAAA,CAAAH,OAAA,MAAApE,IAAA;QAAAgF,QAAA;MAAA;IACA;IACAuC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,UAAAR,WAAA,CAAApJ,MAAA;QACA,KAAA6J,MAAA,CAAAC,YAAA;QACA;MACA;MAEA,UAAA/J,UAAA,UAAAA,UAAA,CAAAC,MAAA;QACA,KAAA6J,MAAA,CAAAC,YAAA;QACA;MACA;MAEA,KAAA/J,UAAA,CAAAE,GAAA,WAAA8J,MAAA;QACAA,MAAA,CAAAV,oBAAA,GAAAO,MAAA,CAAAR,WAAA,IAAAE,SAAA;QACAS,MAAA,CAAAR,cAAA,GAAAK,MAAA,CAAAR,WAAA,IAAAI,gBAAA;QACAO,MAAA,CAAAnB,mBAAA;QACAmB,MAAA,CAAA5J,kBAAA;QACA4J,MAAA,CAAA3C,QAAA;QACAwC,MAAA,CAAApJ,KAAA,gBAAAoJ,MAAA,CAAAH,CAAA,CAAAC,SAAA,CAAAK,MAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAjC,GAAA,EAAAkC,IAAA;MACAlC,GAAA,CAAAmC,UAAA,GAAAD,IAAA;MACA,KAAA5G,SAAA;QACA0E,GAAA,CAAAoC,YAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAArC,GAAA,EAAAkC,IAAA;MACAlC,GAAA,CAAAmC,UAAA,GAAAD,IAAA;MACA,KAAA5G,SAAA;QACA0E,GAAA,CAAAsC,iBAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAvC,GAAA,EAAA3F,IAAA;MACA,IAAA2F,GAAA,CAAAS,eAAA,KAAApG,IAAA,CAAAmG,UAAA;QACAR,GAAA,CAAAQ,UAAA,GAAAnG,IAAA,CAAAoG,eAAA;QACAT,GAAA,CAAAwC,mBAAA;MACA;MACA,IAAAxC,GAAA,CAAAyC,YAAA,YAAApI,IAAA,CAAAoI,YAAA;QACAzC,GAAA,CAAAc,cAAA,GAAAzG,IAAA,CAAAoI,YAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAA1C,GAAA,EAAAyC,YAAA;MACAzC,GAAA,CAAAc,cAAA,GAAA2B,YAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA,KAAAnH,SAAA;QACA;QACA0E,GAAA,CAAA2C,qBAAA;MACA;IACA;IACA,SACA1C,QAAA,WAAAA,SAAA2C,KAAA;MAAA,IAAA5C,GAAA,GAAA4C,KAAA,CAAA5C,GAAA;QAAAC,QAAA,GAAA2C,KAAA,CAAA3C,QAAA;MACAD,GAAA,CAAAgB,EAAA,GAAAf,QAAA;IACA;IACA4C,oBAAA,WAAAA,qBAAA;MACA;MACA,UAAA7K,UAAA;QACA,KAAAS,KAAA;QACA;MACA;MAEA,IAAA0I,GAAA;QACA2B,UAAA;QACAC,YAAA;QACAP,mBAAA;QACAQ,cAAA;QACAL,qBAAA;QACAM,gBAAA;QACAX,iBAAA;QACAF,YAAA;QACAc,YAAA;QACAC,YAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,iBAAA;QACA/J,QAAA;QACAgK,QAAA;QACA;QACA3C,mBAAA,OAAA4C,YAAA;QACAC,iBAAA,OAAA1L,UAAA,CAAAC,MAAA,YAAAD,UAAA,MAAAA,UAAA,CAAAC,MAAA,MAAAyL,iBAAA;MACA;MACA,SAAAC,aAAA,QAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,SAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,UAAAxC,GAAA,CAAAyC,gBAAA;MACA,SAAAD,aAAA,UAAAxC,GAAA,CAAAyC,gBAAA;MACA,KAAA5L,UAAA,CAAA6L,IAAA,CAAA1C,GAAA;;MAEA;MACA,KAAA7B,YAAA;IACA;IACAwE,WAAA,WAAAA,YAAA9D,GAAA,EAAA+D,QAAA;MACA;MACA,KAAA/D,GAAA;;MAEA;MACA,IAAAgE,QAAA,GAAAhE,GAAA,CAAA3G,UAAA;MACA,IAAAP,MAAA,GAAAkH,GAAA,CAAAwD,QAAA;MACA,IAAAS,YAAA,OAAApL,iBAAA,EAAAmH,GAAA,CAAAuD,iBAAA;QAAAtK,SAAA;MAAA,GAAAE,KAAA;MACA,IAAAK,QAAA,OAAAX,iBAAA,EAAAmH,GAAA,CAAAxG,QAAA,OAAAL,KAAA;MAEA;QACA;QACA,IAAAC,QAAA,OAAAP,iBAAA,EAAAmL,QAAA;UAAA/K,SAAA;QAAA,GACAK,QAAA,CAAAR,MAAA,EACAQ,QAAA,CAAA2K,YAAA,EACA3K,QAAA,KAAAT,iBAAA,KAAAU,GAAA,KAAAV,iBAAA,EAAAW,QAAA,EAAAN,MAAA,QACAC,KAAA;;QAEA;QACA6G,GAAA,CAAA5G,QAAA,OAAAP,iBAAA,EAAAO,QAAA;UAAAH,SAAA;QAAA,GAAAE,KAAA;QACA6G,GAAA,CAAAkE,oBAAA,GAAAlE,GAAA,CAAA5H,kBAAA,eAAAS,iBAAA,EAAAO,QAAA;UAAAH,SAAA;QAAA,GAAAE,KAAA,GAAA6G,GAAA,CAAAkE,oBAAA;;QAEA;QACA,QAAAH,QAAA;UACA;YACA/D,GAAA,CAAAkD,YAAA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;QACA;;QAEA;QACA,KAAAzK,KAAA,gBAAAT,UAAA;;QAEA;QACA,KAAAsH,YAAA;MAEA,SAAA7F,KAAA;QACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;QACA,KAAA0K,QAAA,CAAA1K,KAAA;MACA;IACA;IACA2K,UAAA,WAAAA,WAAApE,GAAA;MACA,KAAAvH,KAAA,eAAAuH,GAAA;IACA;IACAqE,aAAA,WAAAA,cAAArE,GAAA;MACA,KAAAvH,KAAA;IACA;IACA6L,iBAAA,WAAAA,kBAAAC,IAAA;MACA;QACAvD,EAAA,EAAAuD,IAAA,CAAAhD,SAAA;QACA3G,KAAA,GAAA2J,IAAA,CAAA9C,gBAAA,WAAA8C,IAAA,CAAA9C,gBAAA,gBAAA8C,IAAA,CAAAC,gBAAA,WAAAD,IAAA,CAAAC,gBAAA,eAAAC,iBAAA,CAAAC,YAAA,EAAAH,IAAA,CAAA9C,gBAAA,WAAA8C,IAAA,CAAA9C,gBAAA,gBAAA8C,IAAA,CAAAC,gBAAA,WAAAD,IAAA,CAAAC,gBAAA;MACA;IACA;IACA;IACAlF,YAAA,WAAAA,aAAA;MAAA,IAAAqF,MAAA;MACA,KAAArJ,SAAA;QACA,IAAAqJ,MAAA,CAAAnJ,KAAA,CAAAC,WAAA;UACAkJ,MAAA,CAAAnJ,KAAA,CAAAC,WAAA,CAAAmJ,QAAA;QACA;MACA;IACA;EACA;AACA;AAAAC,OAAA,CAAApG,OAAA,GAAAqG,QAAA"}]}