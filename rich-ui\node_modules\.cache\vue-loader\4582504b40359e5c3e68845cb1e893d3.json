{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue", "mtime": 1754903721862}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCINCmltcG9ydCBwaW55aW4gZnJvbSAianMtcGlueWluIg0KaW1wb3J0IENvbXBhbnlTZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0NvbXBhbnlTZWxlY3QvaW5kZXgudnVlIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCmltcG9ydCBTb3J0YWJsZSBmcm9tICJzb3J0YWJsZWpzIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJkZWJpdE5vdGVDaGFyZ2VMaXN0IiwNCiAgY29tcG9uZW50czoge0NvbXBhbnlTZWxlY3QsIFRyZWVzZWxlY3R9LA0KICBwcm9wczogWyJjaGFyZ2VEYXRhIiwgImNvbXBhbnlMaXN0IiwgIm9wZW5DaGFyZ2VMaXN0IiwgImlzUmVjZWl2YWJsZSIsICJkaXNhYmxlZCIsDQogICAgImhpZGRlblN1cHBsaWVyIiwgInJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhVU0QiLA0KICAgICJyc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1CIiwgInJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhVU0QiLCAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFJNQiIsDQogICAgInJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVSTUIiLCAicnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVVTRCIsICJyc0NsaWVudE1lc3NhZ2VQYXlhYmxlUk1CIiwNCiAgICAicnNDbGllbnRNZXNzYWdlUGF5YWJsZVVTRCIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXQiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0Tm9UYXgiLCAicGF5RGV0YWlsUk1CIiwNCiAgICAicGF5RGV0YWlsVVNEIiwgInBheURldGFpbFJNQlRheCIsICJwYXlEZXRhaWxVU0RUYXgiLCAicnNDbGllbnRNZXNzYWdlUHJvZml0VVNEIiwgInJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQiIsDQogICAgInJzQ2xpZW50TWVzc2FnZVByb2ZpdFRheFJNQiIsICJyc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QiLCAiZGViaXROb3RlIiwgImRyYWdHcm91cE5hbWUiXSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBoYXNDb25maXJtUm93KCkgew0KICAgICAgbGV0IHJlc3VsdCA9IGZhbHNlOw0KICAgICAgKHRoaXMuY2hhcmdlRGF0YSAmJiB0aGlzLmNoYXJnZURhdGEubGVuZ3RoID4gMCkgPyB0aGlzLmNoYXJnZURhdGEubWFwKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5pc0FjY291bnRDb25maXJtZWQgPT09ICIxIikgew0KICAgICAgICAgIHJlc3VsdCA9IHRydWUNCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICByZXR1cm4gcmVzdWx0DQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIGNoYXJnZURhdGE6IHsNCiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIChuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIW9sZFZhbCkgew0KICAgICAgICAgIHRoaXMuJGVtaXQoInJldHVybiIsIG5ld1ZhbCkNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOmBjeWOhui0ueeUqOWIl+ihqO+8jOajgOafpeW4geenjeWPmOWMlg0KICAgICAgICBuZXdWYWwgPyBuZXdWYWwuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICBjb25zdCBvbGRJdGVtID0gb2xkVmFsW2luZGV4XQ0KDQogICAgICAgICAgLy8g5qOA5p+l5biB56eN5Y+Y5YyW5bm26K6h566X5bCP6K6hDQogICAgICAgICAgaWYgKGl0ZW0uY3VycmVuY3kgJiYgaXRlbS5hbW91bnQpIHsNCiAgICAgICAgICAgIC8vIOWmguaenOS7jiBSTUIg5o2i5oiQIFVTRO+8jOS9v+eUqCAxL+axh+eOhyDorqHnrpcNCiAgICAgICAgICAgIGlmIChvbGRJdGVtICYmIG9sZEl0ZW0uY3VycmVuY3kgPT09ICJSTUIiICYmIGl0ZW0uY3VycmVuY3kgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgIGlmIChpdGVtLmV4Y2hhbmdlUmF0ZSAmJiBpdGVtLmV4Y2hhbmdlUmF0ZSAhPT0gMCkgew0KICAgICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgICAvLyDorqHnrpcgMS/msYfnjofvvIzkv53nlZk05L2N5bCP5pWwDQogICAgICAgICAgICAgICAgICBjb25zdCBpbnZlcnNlUmF0ZSA9IGN1cnJlbmN5KDEsIHtwcmVjaXNpb246IDR9KS5kaXZpZGUoaXRlbS5leGNoYW5nZVJhdGUpLnZhbHVlDQoNCiAgICAgICAgICAgICAgICAgIC8vIOiuoeeul+Wwj+iuoTog5Y2V5Lu3ICog5pWw6YePICog5rGH546HICogKDEgKyDnqI7njocvMTAwKQ0KICAgICAgICAgICAgICAgICAgaXRlbS5zdWJ0b3RhbCA9IGN1cnJlbmN5KGl0ZW0uZG5Vbml0UmF0ZSB8fCAwLCB7cHJlY2lzaW9uOiA0fSkNCiAgICAgICAgICAgICAgICAgICAgLm11bHRpcGx5KGl0ZW0uYW1vdW50KQ0KICAgICAgICAgICAgICAgICAgICAubXVsdGlwbHkoaW52ZXJzZVJhdGUpDQogICAgICAgICAgICAgICAgICAgIC5tdWx0aXBseShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3koaXRlbS5kdXR5UmF0ZSB8fCAwKS5kaXZpZGUoMTAwKSkpDQogICAgICAgICAgICAgICAgICAgIC52YWx1ZQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLorqHnrpflsI/orqHlh7rplJk6IiwgZXJyb3IpDQogICAgICAgICAgICAgICAgICBpdGVtLnN1YnRvdGFsID0gMA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkgOiBudWxsDQoNCiAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgbmV3VmFsID8gbmV3VmFsIDogW10pDQoNCiAgICAgICAgLy8g5pWw5o2u5Y+Y5YyW5ZCO6YeN5paw5Yid5aeL5YyW5ouW5ou977yM5L2/55So5pu05a6J5YWo55qE5pa55byPDQogICAgICAgIHRoaXMucmVpbml0aWFsaXplU29ydGFibGUoKQ0KICAgICAgfSwNCiAgICAgIGRlZXA6IHRydWUsDQogICAgICBpbW1lZGlhdGU6IHRydWUNCiAgICB9LA0KICAgIGRpc2FibGVkOiB7DQogICAgICBoYW5kbGVyOiBmdW5jdGlvbiAobmV3VmFsKSB7DQogICAgICAgIC8vIOemgeeUqOeKtuaAgeaUueWPmOaXtumHjeaWsOWIneWni+WMluaLluaLvQ0KICAgICAgICB0aGlzLnJlaW5pdGlhbGl6ZVNvcnRhYmxlKCkNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0U29ydGFibGUoKQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIC8vIOa4heeQhuWumuaXtuWZqA0KICAgIGlmICh0aGlzLnJlaW5pdFRpbWVyKSB7DQogICAgICBjbGVhclRpbWVvdXQodGhpcy5yZWluaXRUaW1lcikNCiAgICAgIHRoaXMucmVpbml0VGltZXIgPSBudWxsDQogICAgfQ0KICAgIC8vIOmUgOavgeaLluaLveWunuS+iw0KICAgIHRoaXMuZGVzdHJveVNvcnRhYmxlKCkNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGF5VG90YWxSTUI6IDAsDQogICAgICBwYXlUb3RhbFVTRDogMCwNCiAgICAgIHNob3dDbGllbnROYW1lOiBudWxsLA0KICAgICAgc29ydGFibGU6IG51bGwsDQogICAgICByZWluaXRUaW1lcjogbnVsbCwNCiAgICAgIGRyYWdTdGFydENvbHVtbjogLTEsIC8vIOiusOW9leaLluaLveW8gOWni+eahOWIl+e0ouW8lQ0KICAgICAgc2VydmljZXM6IFt7DQogICAgICAgIHZhbHVlOiAxLA0KICAgICAgICBsYWJlbDogIua1t+i/kCINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDEwLA0KICAgICAgICBsYWJlbDogIuepuui/kCINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDIwLA0KICAgICAgICBsYWJlbDogIumTgei3ryINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDQwLA0KICAgICAgICBsYWJlbDogIuW/q+mAkiINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDUwLA0KICAgICAgICBsYWJlbDogIuaLlui9piINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDYwLA0KICAgICAgICBsYWJlbDogIuaKpeWFsyINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDcwLA0KICAgICAgICBsYWJlbDogIua4heWFs+a0vumAgSINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDgwLA0KICAgICAgICBsYWJlbDogIueggeWktOS7k+WCqCINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDkwLA0KICAgICAgICBsYWJlbDogIuajgOmqjOivgeS5piINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDEwMCwNCiAgICAgICAgbGFiZWw6ICLkv53pmakiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiAxMDEsDQogICAgICAgIGxhYmVsOiAi5omp5bGV5pyN5YqhIg0KICAgICAgfV0sDQogICAgICBzZXJ2aWNlOiBbew0KICAgICAgICB2YWx1ZTogMSwNCiAgICAgICAgbGFiZWw6ICLln7rnoYDmnI3liqEiDQogICAgICB9LCB7DQogICAgICAgIHZhbHVlOiA0LA0KICAgICAgICBsYWJlbDogIuWJjeeoi+i/kOi+kyINCiAgICAgIH0sIHsNCiAgICAgICAgdmFsdWU6IDUsDQogICAgICAgIGxhYmVsOiAi5Ye65Y+j5oql5YWzIg0KICAgICAgfSwgew0KICAgICAgICB2YWx1ZTogNiwNCiAgICAgICAgbGFiZWw6ICLov5vlj6PmuIXlhbMiDQogICAgICB9LCB7dmFsdWU6IDIsIGxhYmVsOiAi5rW36L+QIn0NCiAgICAgICAgLCB7dmFsdWU6IDMsIGxhYmVsOiAi6ZmG6L+QIn0NCiAgICAgICAgLCB7dmFsdWU6IDQsIGxhYmVsOiAi6ZOB6LevIn0NCiAgICAgICAgLCB7dmFsdWU6IDUsIGxhYmVsOiAi56m66L+QIn0NCiAgICAgICAgLCB7dmFsdWU6IDYsIGxhYmVsOiAi5b+r6YCSIn0NCiAgICAgICAgLCB7dmFsdWU6IDIxLCBsYWJlbDogIuaVtOafnOa1t+i/kCJ9DQogICAgICAgICwge3ZhbHVlOiAyMiwgbGFiZWw6ICLmi7zmn5zmtbfov5AifQ0KICAgICAgICAsIHt2YWx1ZTogMjMsIGxhYmVsOiAi5pWj5p2C6Ii5In0NCiAgICAgICAgLCB7dmFsdWU6IDI0LCBsYWJlbDogIua7muijheiIuSJ9DQogICAgICAgICwge3ZhbHVlOiA0MSwgbGFiZWw6ICLmlbTmn5zpk4Hot68ifQ0KICAgICAgICAsIHt2YWx1ZTogNDIsIGxhYmVsOiAi5ou85p+c6ZOB6LevIn0NCiAgICAgICAgLCB7dmFsdWU6IDQzLCBsYWJlbDogIumTgei3r+i9pueariJ9DQogICAgICAgICwge3ZhbHVlOiA1MSwgbGFiZWw6ICLnqbrov5Dmma7oiLEifQ0KICAgICAgICAsIHt2YWx1ZTogNTIsIGxhYmVsOiAi56m66L+Q5YyF5p2/In0NCiAgICAgICAgLCB7dmFsdWU6IDUzLCBsYWJlbDogIuepuui/kOWMheacuiJ9DQogICAgICAgICwge3ZhbHVlOiA1NCwgbGFiZWw6ICLnqbrov5DooYzmnY4ifQ0KICAgICAgICAsIHt2YWx1ZTogOTYxLCBsYWJlbDogIuWJjeeoi+i/kOi+kyJ9DQogICAgICAgICwge3ZhbHVlOiA5NjQsIGxhYmVsOiAi6L+b5Y+j5riF5YWzIn0NCiAgICAgICAgLCB7dmFsdWU6IDcsIGxhYmVsOiAi5Ye65Y+j5oql5YWzIn0NCiAgICAgIF0sDQogICAgICBjaGFyZ2VSZW1hcms6IG51bGwNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBkZXN0cm95U29ydGFibGUoKSB7DQogICAgICBpZiAodGhpcy5zb3J0YWJsZSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOajgOafpSBzb3J0YWJsZSDlrp7kvovmmK/lkKbov5jmnInmlYgNCiAgICAgICAgICBpZiAodGhpcy5zb3J0YWJsZS5lbCAmJiB0aGlzLnNvcnRhYmxlLmVsLnBhcmVudE5vZGUpIHsNCiAgICAgICAgICAgIHRoaXMuc29ydGFibGUuZGVzdHJveSgpDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUud2FybignRXJyb3IgZGVzdHJveWluZyBzb3J0YWJsZTonLCBlcnJvcikNCiAgICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgICB0aGlzLnNvcnRhYmxlID0gbnVsbA0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICByZWluaXRpYWxpemVTb3J0YWJsZSgpIHsNCiAgICAgIC8vIOS9v+eUqOmYsuaKluW7tui/n+mHjeaWsOWIneWni+WMlu+8jOmBv+WFjemikee5geeahOWIm+W7uumUgOavgQ0KICAgICAgaWYgKHRoaXMucmVpbml0VGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucmVpbml0VGltZXIpDQogICAgICB9DQoNCiAgICAgIHRoaXMucmVpbml0VGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuZGVzdHJveVNvcnRhYmxlKCkNCiAgICAgICAgICAvLyDnoa7kv50gRE9NIOW3suabtOaWsOWQjuWGjeWIneWni+WMlg0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuaW5pdFNvcnRhYmxlKCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KICAgICAgfSwgMTAwKQ0KICAgIH0sDQogICAgaW5pdFNvcnRhYmxlKCkgew0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBpZiAodGhpcy4kcmVmcy5jaGFyZ2VUYWJsZSAmJiB0aGlzLiRyZWZzLmNoYXJnZVRhYmxlLiRlbCkgew0KICAgICAgICAgIGNvbnN0IHRib2R5ID0gdGhpcy4kcmVmcy5jaGFyZ2VUYWJsZS4kZWwucXVlcnlTZWxlY3RvcigndGJvZHknKQ0KICAgICAgICAgIGlmICh0Ym9keSkgew0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgdGhpcy5zb3J0YWJsZSA9IFNvcnRhYmxlLmNyZWF0ZSh0Ym9keSwgew0KICAgICAgICAgICAgICAgIGdyb3VwOiB7DQogICAgICAgICAgICAgICAgICBuYW1lOiB0aGlzLmRyYWdHcm91cE5hbWUgfHwgJ2RlYml0Tm90ZUNoYXJnZUdyb3VwJywNCiAgICAgICAgICAgICAgICAgIHB1bGw6ICh0bywgZnJvbSwgZHJhZ0VsLCBldnQpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgLy8g5qC55o2u5ouW5ou95YiX5Yaz5a6a5piv5ZCm5YWB6K645ouW5Ye677yI5Ymq5YiH5oiW5aSN5Yi277yJDQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmRyYWdTdGFydENvbHVtbiA9PT0gMSkgew0KICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlOyAvLyDku47nrKzkuIDliJfmi5bliqjml7blhYHorrjmi5blh7rvvIjliarliIfvvIkNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ2Nsb25lJzsgLy8g5LuO5YW25LuW5YiX5ouW5Yqo5pe25aSN5Yi2DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICBwdXQ6ICF0aGlzLmRpc2FibGVkDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBhbmltYXRpb246IDE1MCwNCiAgICAgICAgICAgICAgICBkaXNhYmxlZDogdGhpcy5kaXNhYmxlZCwNCiAgICAgICAgICAgICAgICBnaG9zdENsYXNzOiAnc29ydGFibGUtZ2hvc3QnLA0KICAgICAgICAgICAgICAgIGRyYWdDbGFzczogJ3NvcnRhYmxlLWRyYWcnLA0KICAgICAgICAgICAgICAgIGZpbHRlcjogJy5kaXNhYmxlZCcsDQogICAgICAgICAgICAgICAgb25TdGFydDogKGV2dCkgPT4gew0KICAgICAgICAgICAgICAgICAgLy8g5ouW5ou95byA5aeLDQogICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuY2hhcmdlRGF0YSB8fCAhdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCkgcmV0dXJuDQogICAgICAgICAgICAgICAgICBjb25zdCBkcmFnZ2VkSXRlbSA9IHRoaXMuY2hhcmdlRGF0YVtldnQub2xkSW5kZXhdDQoNCiAgICAgICAgICAgICAgICAgIC8vIOiOt+WPluaLluaLveW8gOWni+eahOWIl+e0ouW8lQ0KICAgICAgICAgICAgICAgICAgdGhpcy5kcmFnU3RhcnRDb2x1bW4gPSAtMSAvLyDpu5jorqTorr7nva7kuLotMQ0KICAgICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgICAgLy8g6I635Y+W5ouW5ou95LqL5Lu255qE6LW35aeL5Z2Q5qCHDQogICAgICAgICAgICAgICAgICAgIGNvbnN0IG1vdXNlWCA9IGV2dC5vcmlnaW5hbEV2ZW50LmNsaWVudFgNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgbW91c2VZID0gZXZ0Lm9yaWdpbmFsRXZlbnQuY2xpZW50WQ0KDQogICAgICAgICAgICAgICAgICAgIC8vIOWwneivleabtOWHhuehruWcsOehruWumuaLluaLveW8gOWni+eahOWIlw0KICAgICAgICAgICAgICAgICAgICBjb25zdCBjZWxscyA9IGV2dC5pdGVtLnF1ZXJ5U2VsZWN0b3JBbGwoJ3RkJykNCiAgICAgICAgICAgICAgICAgICAgaWYgKGNlbGxzICYmIGNlbGxzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDorqHnrpfmr4/kuKrljZXlhYPmoLznmoTkvY3nva7vvIzmib7liLDljIXlkKvpvKDmoIfkvY3nva7nmoTljZXlhYPmoLwNCiAgICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNlbGxzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZWN0ID0gY2VsbHNbaV0uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtb3VzZVggPj0gcmVjdC5sZWZ0ICYmIG1vdXNlWCA8PSByZWN0LnJpZ2h0ICYmDQogICAgICAgICAgICAgICAgICAgICAgICAgIG1vdXNlWSA+PSByZWN0LnRvcCAmJiBtb3VzZVkgPD0gcmVjdC5ib3R0b20pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5kcmFnU3RhcnRDb2x1bW4gPSBpDQogICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgLy8g5aSH6YCJ5pa55rOV77ya5aaC5p6c5LiK6Z2i55qE5pa55rOV5rKh5om+5Yiw77yM5L2/55So6KGo5aS05a6a5L2NDQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmRyYWdTdGFydENvbHVtbiA9PT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICBjb25zdCBoZWFkZXJDZWxscyA9IHRoaXMuJHJlZnMuY2hhcmdlVGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3JBbGwoJ3RoZWFkIHRoJykNCiAgICAgICAgICAgICAgICAgICAgICBpZiAoaGVhZGVyQ2VsbHMgJiYgaGVhZGVyQ2VsbHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBoZWFkZXJDZWxscy5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZWN0ID0gaGVhZGVyQ2VsbHNbaV0uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkNCiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG1vdXNlWCA+PSByZWN0LmxlZnQgJiYgbW91c2VYIDw9IHJlY3QucmlnaHQpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmRyYWdTdGFydENvbHVtbiA9IGkNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaw0KICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMuZHJhZ1N0YXJ0Q29sdW1uID09PSAtMSkgew0KICAgICAgICAgICAgICAgICAgICAgIC8vIOWbnumAgOaWueahiO+8muWmguaenOmAmui/h+WdkOagh+aXoOazleehruWumu+8jOWImem7mOiupOS4uumdnuesrOS4gOWIlw0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZHJhZ1N0YXJ0Q29sdW1uID0gMiAvLyDorr7nva7kuLrpnZ7nrKzkuIDliJfvvIzpu5jorqTkuLrlpI3liLbmqKHlvI8NCiAgICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfnoa7lrprmi5bmi73lvIDlp4vliJfml7blh7rplJk6JywgZXJyb3IpDQogICAgICAgICAgICAgICAgICAgIHRoaXMuZHJhZ1N0YXJ0Q29sdW1uID0gMSAvLyDlh7rplJnml7bpu5jorqTkuLrnrKzkuIDliJcNCiAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgLy8g6K6+572u6KKr5ouW5ou95YWD57Sg55qE5pWw5o2uDQogICAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgICBldnQuaXRlbS5zZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1pdGVtJywgSlNPTi5zdHJpbmdpZnkoZHJhZ2dlZEl0ZW0pKQ0KICAgICAgICAgICAgICAgICAgICAvLyDpop3lpJbmt7vliqDmi5bmi73otbflp4vliJfkv6Hmga8NCiAgICAgICAgICAgICAgICAgICAgZXZ0Lml0ZW0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctY29sdW1uJywgdGhpcy5kcmFnU3RhcnRDb2x1bW4pDQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3RyaW5naWZ5IGRyYWcgaXRlbTonLCBlcnJvcikNCiAgICAgICAgICAgICAgICAgICAgZXZ0Lml0ZW0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScsICd7fScpDQogICAgICAgICAgICAgICAgICAgIGV2dC5pdGVtLnNldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWNvbHVtbicsICctMScpDQogICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2RyYWdTdGFydCcsIHsNCiAgICAgICAgICAgICAgICAgICAgaXRlbTogZHJhZ2dlZEl0ZW0sDQogICAgICAgICAgICAgICAgICAgIGluZGV4OiBldnQub2xkSW5kZXgsDQogICAgICAgICAgICAgICAgICAgIGZyb206IHRoaXMsDQogICAgICAgICAgICAgICAgICAgIGNvbHVtbjogdGhpcy5kcmFnU3RhcnRDb2x1bW4NCiAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBvbkFkZDogKGV2dCkgPT4gew0KICAgICAgICAgICAgICAgICAgLy8g5o6l5pS25Yiw5paw5YWD57SgDQogICAgICAgICAgICAgICAgICBpZiAoIXRoaXMuY2hhcmdlRGF0YSkgcmV0dXJuDQogICAgICAgICAgICAgICAgICBjb25zdCBpdGVtID0gZXZ0Lml0ZW0NCiAgICAgICAgICAgICAgICAgIGxldCBkcmFnZ2VkSXRlbSA9IHt9DQogICAgICAgICAgICAgICAgICBsZXQgZHJhZ1N0YXJ0Q29sdW1uID0gLTENCg0KICAgICAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgZHJhZ0RhdGEgPSBpdGVtLmdldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWl0ZW0nKQ0KICAgICAgICAgICAgICAgICAgICBpZiAoZHJhZ0RhdGEgJiYgZHJhZ0RhdGEgIT09ICd1bmRlZmluZWQnKSB7DQogICAgICAgICAgICAgICAgICAgICAgZHJhZ2dlZEl0ZW0gPSBKU09OLnBhcnNlKGRyYWdEYXRhKQ0KICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgLy8g6I635Y+W5ouW5ou96LW35aeL5YiXDQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbHVtbkRhdGEgPSBpdGVtLmdldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWNvbHVtbicpDQogICAgICAgICAgICAgICAgICAgIGlmIChjb2x1bW5EYXRhICYmIGNvbHVtbkRhdGEgIT09ICd1bmRlZmluZWQnKSB7DQogICAgICAgICAgICAgICAgICAgICAgZHJhZ1N0YXJ0Q29sdW1uID0gcGFyc2VJbnQoY29sdW1uRGF0YSwgMTApDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwYXJzZSBkcmFnIGl0ZW0gZGF0YTonLCBlcnJvcikNCiAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgLy8g5aSE55CG5paw5aKe5YWD57Sg5Yiw6KGo5qC8DQogICAgICAgICAgICAgICAgICBjb25zdCBuZXdDaGFyZ2VEYXRhID0gWy4uLnRoaXMuY2hhcmdlRGF0YV0NCg0KICAgICAgICAgICAgICAgICAgLy8g5peg6K665piv5aSN5Yi26L+Y5piv5Ymq5YiH77yM6YO96ZyA6KaB5re75Yqg6aG55Yiw55uu5qCH5L2N572uDQogICAgICAgICAgICAgICAgICAvLyDkvYbopoHnu5nmlrDpobnnlJ/miJDkuIDkuKrmlrDnmoRJRO+8jOihqOekuui/meaYr+S4gOS4quWFqOaWsOeahOmhuQ0KICAgICAgICAgICAgICAgICAgbmV3Q2hhcmdlRGF0YS5zcGxpY2UoZXZ0Lm5ld0luZGV4LCAwLCB7DQogICAgICAgICAgICAgICAgICAgIC4uLmRyYWdnZWRJdGVtLA0KICAgICAgICAgICAgICAgICAgICB0ZW1wSWQ6IGB0ZW1wXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCwNCiAgICAgICAgICAgICAgICAgICAgY2hhcmdlSWQ6IG51bGwgLy8g5riF6Zmk5Y6f5pyJSUTvvIzkvZzkuLrmlrDlop7pobkNCiAgICAgICAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoJ3JldHVybicsIG5ld0NoYXJnZURhdGEpDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnZHJhZ0FkZCcsIHsNCiAgICAgICAgICAgICAgICAgICAgIGl0ZW06IGRyYWdnZWRJdGVtLA0KICAgICAgICAgICAgICAgICAgICAgbmV3SW5kZXg6IGV2dC5uZXdJbmRleCwNCiAgICAgICAgICAgICAgICAgICAgIHRvOiB0aGlzLA0KICAgICAgICAgICAgICAgICAgICAgY29sdW1uOiBkcmFnU3RhcnRDb2x1bW4NCiAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgICAgIC8vIOaLluaLvea3u+WKoOWQjuWIt+aWsOihqOagvA0KICAgICAgICAgICAgICAgICAgIHRoaXMucmVmcmVzaFRhYmxlKCkNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIG9uUmVtb3ZlOiAoZXZ0KSA9PiB7DQogICAgICAgICAgICAgICAgICAvLyDojrflj5bmi5bmi73kv6Hmga8NCiAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5jaGFyZ2VEYXRhKSByZXR1cm4NCiAgICAgICAgICAgICAgICAgIGNvbnN0IGl0ZW0gPSBldnQuaXRlbQ0KICAgICAgICAgICAgICAgICAgbGV0IGRyYWdTdGFydENvbHVtbiA9IC0xDQogICAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgICBjb25zdCBjb2x1bW5EYXRhID0gaXRlbS5nZXRBdHRyaWJ1dGUoJ2RhdGEtZHJhZy1jb2x1bW4nKQ0KICAgICAgICAgICAgICAgICAgICBpZiAoY29sdW1uRGF0YSAmJiBjb2x1bW5EYXRhICE9PSAndW5kZWZpbmVkJykgew0KICAgICAgICAgICAgICAgICAgICAgIGRyYWdTdGFydENvbHVtbiA9IHBhcnNlSW50KGNvbHVtbkRhdGEsIDEwKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcGFyc2UgZHJhZyBjb2x1bW4gZGF0YTonLCBlcnJvcikNCiAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgY29uc3QgbmV3Q2hhcmdlRGF0YSA9IFsuLi50aGlzLmNoYXJnZURhdGFdDQogICAgICAgICAgICAgICAgICBjb25zdCByZW1vdmVkSXRlbSA9IG5ld0NoYXJnZURhdGFbZXZ0Lm9sZEluZGV4XQ0KDQogICAgICAgICAgICAgICAgICAvLyDlj6rmnInlnKjku47nrKzkuIDliJflvIDlp4vmi5bmi73ml7bmiY3miafooYzliarliIfmk43kvZwNCiAgICAgICAgICAgICAgICAgIC8vIFNvcnRhYmxl55qEY2xvbmXpgInpobnlt7Lnu4/mjqfliLbkuoblpI3liLbooYzkuLrvvIzov5nph4zmiJHku6zlj6rpnIDlpITnkIbliarliIfnmoTmg4XlhrUNCiAgICAgICAgICAgICAgICAgIGlmIChkcmFnU3RhcnRDb2x1bW4gPT09IDEpIHsNCiAgICAgICAgICAgICAgICAgICAgbmV3Q2hhcmdlRGF0YS5zcGxpY2UoZXZ0Lm9sZEluZGV4LCAxKQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdyZXR1cm4nLCBuZXdDaGFyZ2VEYXRhKQ0KICAgICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnZHJhZ1JlbW92ZScsIHsNCiAgICAgICAgICAgICAgICAgICAgIGl0ZW06IHJlbW92ZWRJdGVtLA0KICAgICAgICAgICAgICAgICAgICAgb2xkSW5kZXg6IGV2dC5vbGRJbmRleCwNCiAgICAgICAgICAgICAgICAgICAgIGZyb206IHRoaXMsDQogICAgICAgICAgICAgICAgICAgICBjb2x1bW46IGRyYWdTdGFydENvbHVtbiwNCiAgICAgICAgICAgICAgICAgICAgIGlzQ3V0OiBkcmFnU3RhcnRDb2x1bW4gPT09IDENCiAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgICAgIC8vIOaLluaLveenu+mZpOWQjuWIt+aWsOihqOagvA0KICAgICAgICAgICAgICAgICAgIHRoaXMucmVmcmVzaFRhYmxlKCkNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIG9uVXBkYXRlOiAoZXZ0KSA9PiB7DQogICAgICAgICAgICAgICAgICAvLyDlkIzkuIDooajmoLzlhoXmjpLluo8NCiAgICAgICAgICAgICAgICAgIGlmICghdGhpcy5jaGFyZ2VEYXRhKSByZXR1cm4NCiAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0NoYXJnZURhdGEgPSBbLi4udGhpcy5jaGFyZ2VEYXRhXQ0KICAgICAgICAgICAgICAgICAgY29uc3QgaXRlbSA9IG5ld0NoYXJnZURhdGEuc3BsaWNlKGV2dC5vbGRJbmRleCwgMSlbMF0NCiAgICAgICAgICAgICAgICAgIG5ld0NoYXJnZURhdGEuc3BsaWNlKGV2dC5uZXdJbmRleCwgMCwgaXRlbSkNCg0KICAgICAgICAgICAgICAgICAgdGhpcy4kZW1pdCgncmV0dXJuJywgbmV3Q2hhcmdlRGF0YSkNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRlbWl0KCdkcmFnVXBkYXRlJywgew0KICAgICAgICAgICAgICAgICAgICAgaXRlbTogaXRlbSwNCiAgICAgICAgICAgICAgICAgICAgIG9sZEluZGV4OiBldnQub2xkSW5kZXgsDQogICAgICAgICAgICAgICAgICAgICBuZXdJbmRleDogZXZ0Lm5ld0luZGV4LA0KICAgICAgICAgICAgICAgICAgICAgY29sdW1uOiB0aGlzLmRyYWdTdGFydENvbHVtbg0KICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgICAgLy8g5ouW5ou95pu05paw5ZCO5Yi35paw6KGo5qC8DQogICAgICAgICAgICAgICAgICAgdGhpcy5yZWZyZXNoVGFibGUoKQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkVuZDogKGV2dCkgPT4gew0KICAgICAgICAgICAgICAgICAgIC8vIOaLluaLvee7k+adnw0KICAgICAgICAgICAgICAgICAgIHRoaXMuJGVtaXQoJ2RyYWdFbmQnLCB7DQogICAgICAgICAgICAgICAgICAgICAuLi5ldnQsDQogICAgICAgICAgICAgICAgICAgICBkcmFnQ29sdW1uOiB0aGlzLmRyYWdTdGFydENvbHVtbg0KICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgLy8g6YeN572u5ouW5ou95YiXDQogICAgICAgICAgICAgICAgICAgdGhpcy5kcmFnU3RhcnRDb2x1bW4gPSAtMQ0KICAgICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgICAgIC8vIOaLluaLvee7k+adn+WQjuWIt+aWsOihqOagvA0KICAgICAgICAgICAgICAgICAgIHRoaXMucmVmcmVzaFRhYmxlKCkNCiAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgc29ydGFibGU6JywgZXJyb3IpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgc2V0Um93RGF0YSh7cm93LCByb3dJbmRleH0pIHsNCiAgICAgIC8vIOS4uuavj+ihjOiuvue9ruaVsOaNruWxnuaAp++8jOeUqOS6juaLluaLveS8oOmAkuaVsOaNrg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCB0YWJsZVJvd3MgPSB0aGlzLiRyZWZzLmNoYXJnZVRhYmxlLiRlbC5xdWVyeVNlbGVjdG9yQWxsKCd0Ym9keSB0cicpDQogICAgICAgIGlmICh0YWJsZVJvd3Nbcm93SW5kZXhdKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRhYmxlUm93c1tyb3dJbmRleF0uc2V0QXR0cmlidXRlKCdkYXRhLWRyYWctaXRlbScsIEpTT04uc3RyaW5naWZ5KHJvdykpDQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzdHJpbmdpZnkgcm93IGRhdGE6JywgZXJyb3IpDQogICAgICAgICAgICB0YWJsZVJvd3Nbcm93SW5kZXhdLnNldEF0dHJpYnV0ZSgnZGF0YS1kcmFnLWl0ZW0nLCAne30nKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHJldHVybiAnJw0KICAgIH0sDQogICAgYXVkaXRTdGF0dXMoc3RhdHVzKSB7DQogICAgICByZXR1cm4gc3RhdHVzID09IDEgPyAi5bey5a6h5qC4IiA6ICLmnKrlrqHmoLgiDQogICAgfSwNCiAgICBzZWxlY3RDaGFyZ2UodGFyZ2V0LCByb3cpIHsNCiAgICAgIHJvdy5kbkNoYXJnZU5hbWVJZCA9IHRhcmdldC5jaGFyZ2VJZA0KICAgICAgcm93LmNoYXJnZU5hbWUgPSB0YXJnZXQuY2hhcmdlTG9jYWxOYW1lDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7DQogICAgICAvLyDlpITnkIbpgInmi6nlj5jljJYNCiAgICAgIHRoaXMuJGVtaXQoInNlbGVjdFJvdyIsIHZhbCkNCg0KICAgICAgdGhpcy5wYXlUb3RhbFVTRCA9IDANCiAgICAgIHRoaXMucGF5VG90YWxSTUIgPSAwDQogICAgICB2YWwgPyB2YWwubWFwKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5pc1JlY2lldmluZ09yUGF5aW5nID09IDEpIHsNCiAgICAgICAgICBpZiAoaXRlbS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHRoaXMucGF5VG90YWxVU0QgPSBjdXJyZW5jeSh0aGlzLnBheVRvdGFsVVNEKS5hZGQoaXRlbS5zdWJ0b3RhbCkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5wYXlUb3RhbFJNQiA9IGN1cnJlbmN5KHRoaXMucGF5VG90YWxSTUIpLmFkZChpdGVtLnN1YnRvdGFsKQ0KICAgICAgICAgIH0NCg0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCg0KICAgIH0sDQogICAgY3VycmVuY3ksDQogICAgLy8g6I635Y+W6aG555uu55qE5ZSv5LiA6ZSuDQogICAgZ2V0SXRlbUtleShpdGVtKSB7DQogICAgICByZXR1cm4gaXRlbS50ZW1wSWQgfHwgaXRlbS5jaGFyZ2VJZCB8fCBpdGVtLmlkIHx8IGBpdGVtXyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWANCiAgICB9LA0KICAgIGdldFNlcnZpY2VOYW1lKGlkKSB7DQogICAgICBsZXQgc2VydmljZU5hbWUgPSAiIg0KICAgICAgdGhpcy5zZXJ2aWNlcy5tYXAob2JqID0+IHsNCiAgICAgICAgb2JqLnZhbHVlID09PSBpZCA/IHNlcnZpY2VOYW1lID0gb2JqLmxhYmVsIDogbnVsbA0KICAgICAgfSkNCiAgICAgIHJldHVybiBzZXJ2aWNlTmFtZQ0KICAgIH0sDQogICAgY29weUZyZWlnaHQocm93KSB7DQogICAgICBpZiAodGhpcy5jb21wYW55TGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHJvdy5wYXlDbGVhcmluZ0NvbXBhbnlJZCA9IHRoaXMuY29tcGFueUxpc3RbMF0uY29tcGFueUlkDQogICAgICAgIHJvdy5wYXlDb21wYW55TmFtZSA9IHRoaXMuY29tcGFueUxpc3RbMF0uY29tcGFueVNob3J0TmFtZQ0KICAgICAgfQ0KICAgICAgcm93LmlzQWNjb3VudENvbmZpcm1lZCA9IDANCiAgICAgIC8vIOaKpeS7t+WIl+ihqOi3s+i9rOiuouiIseaXtuayoeacieWFrOWPuOWIl+ihqCzlpI3liLbliLDlupTmlLbmsqHmnInlrqLmiLfkv6Hmga8NCiAgICAgIGxldCBkYXRhID0gdGhpcy5fLmNsb25lRGVlcChyb3cpDQoNCiAgICAgIHRoaXMuJGVtaXQoImNvcHlGcmVpZ2h0Iiwgey4uLmRhdGEsIGNoYXJnZUlkOiBudWxsfSkNCiAgICB9LA0KICAgIGNvcHlBbGxGcmVpZ2h0KCkgew0KICAgICAgaWYgKCF0aGlzLmNvbXBhbnlMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwuYWxlcnRXYXJuaW5nKCLor7flhYjpgInmi6nlp5TmiZjljZXkvY3miJblhbPogZTljZXkvY0iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgaWYgKCF0aGlzLmNoYXJnZURhdGEgfHwgIXRoaXMuY2hhcmdlRGF0YS5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwuYWxlcnRXYXJuaW5nKCLmmoLml6DotLnnlKjmlbDmja7lj6/lpI3liLYiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5jaGFyZ2VEYXRhLm1hcChjaGFyZ2UgPT4gew0KICAgICAgICBjaGFyZ2UucGF5Q2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLmNvbXBhbnlMaXN0WzBdLmNvbXBhbnlJZA0KICAgICAgICBjaGFyZ2UucGF5Q29tcGFueU5hbWUgPSB0aGlzLmNvbXBhbnlMaXN0WzBdLmNvbXBhbnlTaG9ydE5hbWUNCiAgICAgICAgY2hhcmdlLmlzUmVjaWV2aW5nT3JQYXlpbmcgPSAwDQogICAgICAgIGNoYXJnZS5pc0FjY291bnRDb25maXJtZWQgPSAwDQogICAgICAgIGNoYXJnZS5jaGFyZ2VJZCA9IG51bGwNCiAgICAgICAgdGhpcy4kZW1pdCgiY29weUZyZWlnaHQiLCB0aGlzLl8uY2xvbmVEZWVwKGNoYXJnZSkpDQogICAgICB9KQ0KICAgIH0sDQogICAgY2hhbmdlVW5pdENvc3Qocm93LCB1bml0KSB7DQogICAgICByb3cuZG5Vbml0Q29kZSA9IHVuaXQNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgcm93LnNob3dDb3N0VW5pdCA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgY2hhbmdlVW5pdChyb3csIHVuaXQpIHsNCiAgICAgIHJvdy5kblVuaXRDb2RlID0gdW5pdA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICByb3cuc2hvd1F1b3RhdGlvblVuaXQgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNoYXJnZVNlbGVjdChyb3csIGRhdGEpIHsNCiAgICAgIGlmIChyb3cuY2hhcmdlTG9jYWxOYW1lID09PSBkYXRhLmNoYXJnZU5hbWUpIHsNCiAgICAgICAgcm93LmNoYXJnZU5hbWUgPSBkYXRhLmNoYXJnZUxvY2FsTmFtZQ0KICAgICAgICByb3cuc2hvd1F1b3RhdGlvbkNoYXJnZSA9IGZhbHNlDQogICAgICB9DQogICAgICBpZiAocm93LmN1cnJlbmN5Q29kZSA9PSBudWxsICYmIGRhdGEuY3VycmVuY3lDb2RlKSB7DQogICAgICAgIHJvdy5kbkN1cnJlbmN5Q29kZSA9IGRhdGEuY3VycmVuY3lDb2RlDQogICAgICB9DQogICAgfSwNCiAgICBjaGFuZ2VDdXJyZW5jeShyb3csIGN1cnJlbmN5Q29kZSkgew0KICAgICAgcm93LmRuQ3VycmVuY3lDb2RlID0gY3VycmVuY3lDb2RlDQogICAgICAvKiBsZXQgZXhjaGFuZ2VSYXRlDQogICAgICBpZiAoY3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5leGNoYW5nZVJhdGVMaXN0KSB7DQogICAgICAgICAgaWYgKGEubG9jYWxDdXJyZW5jeSA9PT0gIlJNQiINCiAgICAgICAgICAgICYmIHJvdy5kbkN1cnJlbmN5Q29kZSA9PSBhLm92ZXJzZWFDdXJyZW5jeQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgZXhjaGFuZ2VSYXRlID0gY3VycmVuY3koYS5zZXR0bGVSYXRlKS5kaXZpZGUoYS5iYXNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSAqLw0KDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIC8vIHJvdy5iYXNpY0N1cnJlbmN5UmF0ZSA9IGV4Y2hhbmdlUmF0ZSA/IGV4Y2hhbmdlUmF0ZSA6IDENCiAgICAgICAgcm93LnNob3dRdW90YXRpb25DdXJyZW5jeSA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOW6j+WPtyAqLw0KICAgIHJvd0luZGV4KHtyb3csIHJvd0luZGV4fSkgew0KICAgICAgcm93LmlkID0gcm93SW5kZXggKyAxDQogICAgfSwNCiAgICBhZGRSZWNlaXZhYmxlUGF5YWJsZSgpIHsNCiAgICAgIC8vIOehruS/nSBjaGFyZ2VEYXRhIOWtmOWcqA0KICAgICAgaWYgKCF0aGlzLmNoYXJnZURhdGEpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgW10pDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBsZXQgb2JqID0gew0KICAgICAgICBzaG93Q2xpZW50OiB0cnVlLA0KICAgICAgICBzaG93U3VwcGxpZXI6IHRydWUsDQogICAgICAgIHNob3dRdW90YXRpb25DaGFyZ2U6IHRydWUsDQogICAgICAgIHNob3dDb3N0Q2hhcmdlOiB0cnVlLA0KICAgICAgICBzaG93UXVvdGF0aW9uQ3VycmVuY3k6IHRydWUsDQogICAgICAgIHNob3dDb3N0Q3VycmVuY3k6IHRydWUsDQogICAgICAgIHNob3dRdW90YXRpb25Vbml0OiB0cnVlLA0KICAgICAgICBzaG93Q29zdFVuaXQ6IHRydWUsDQogICAgICAgIHNob3dTdHJhdGVneTogdHJ1ZSwNCiAgICAgICAgc2hvd1VuaXRSYXRlOiB0cnVlLA0KICAgICAgICBzaG93QW1vdW50OiB0cnVlLA0KICAgICAgICBzaG93Q3VycmVuY3lSYXRlOiB0cnVlLA0KICAgICAgICBzaG93RHV0eVJhdGU6IHRydWUsDQogICAgICAgIGJhc2ljQ3VycmVuY3lSYXRlOiAxLA0KICAgICAgICBkdXR5UmF0ZTogMCwNCiAgICAgICAgZG5BbW91bnQ6IDEsDQogICAgICAgIC8vIOW6lOaUtui/mOaYr+W6lOS7mA0KICAgICAgICBpc1JlY2lldmluZ09yUGF5aW5nOiB0aGlzLmlzUmVjZWl2YWJsZSA/IDAgOiAxLA0KICAgICAgICBjbGVhcmluZ0NvbXBhbnlJZDogdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCA+IDAgPyB0aGlzLmNoYXJnZURhdGFbdGhpcy5jaGFyZ2VEYXRhLmxlbmd0aCAtIDFdLmNsZWFyaW5nQ29tcGFueUlkIDogbnVsbA0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMSkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSAxMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gMjApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gMjANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDQwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDQwDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA1MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA1MA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gNjApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gNjANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDcwKSBvYmouc3FkU2VydmljZVR5cGVJZCA9IDcwDQogICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSA4MCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSA4MA0KICAgICAgaWYgKHRoaXMuc2VydmljZVR5cGVJZCA9PT0gOTApIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gOTANCiAgICAgIGlmICh0aGlzLnNlcnZpY2VUeXBlSWQgPT09IDEwMCkgb2JqLnNxZFNlcnZpY2VUeXBlSWQgPSAxMDANCiAgICAgICAgICAgICBpZiAodGhpcy5zZXJ2aWNlVHlwZUlkID09PSAxMDEpIG9iai5zcWRTZXJ2aWNlVHlwZUlkID0gMTAxDQogICAgICAgdGhpcy5jaGFyZ2VEYXRhLnB1c2gob2JqKQ0KICAgICAgIA0KICAgICAgIC8vIOa3u+WKoOaWsOmhueebruWQjuWIt+aWsOihqOagvA0KICAgICAgIHRoaXMucmVmcmVzaFRhYmxlKCkNCiAgICB9LA0KICAgIGNvdW50UHJvZml0KHJvdywgY2F0ZWdvcnkpIHsNCiAgICAgIC8vIOehruS/neaJgOacieW/heimgeeahOWAvOmDveWtmOWcqOS4lOacieaViA0KICAgICAgaWYgKCFyb3cpIHJldHVybg0KDQogICAgICAvLyDkvb/nlKhjdXJyZW5jeS5qc+adpeWkhOeQhuaVsOWAvCzpgb/lhY3nsr7luqbmjZ/lpLENCiAgICAgIGNvbnN0IHVuaXRSYXRlID0gcm93LmRuVW5pdFJhdGUgfHwgMA0KICAgICAgY29uc3QgYW1vdW50ID0gcm93LmRuQW1vdW50IHx8IDANCiAgICAgIGNvbnN0IGN1cnJlbmN5UmF0ZSA9IGN1cnJlbmN5KHJvdy5iYXNpY0N1cnJlbmN5UmF0ZSB8fCAxLCB7cHJlY2lzaW9uOiA0fSkudmFsdWUNCiAgICAgIGNvbnN0IGR1dHlSYXRlID0gY3VycmVuY3kocm93LmR1dHlSYXRlIHx8IDApLnZhbHVlDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiuoeeul+Wwj+iuoQ0KICAgICAgICBjb25zdCBzdWJ0b3RhbCA9IGN1cnJlbmN5KHVuaXRSYXRlLCB7cHJlY2lzaW9uOiA0fSkNCiAgICAgICAgICAubXVsdGlwbHkoYW1vdW50KQ0KICAgICAgICAgIC5tdWx0aXBseShjdXJyZW5jeVJhdGUpDQogICAgICAgICAgLm11bHRpcGx5KGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShkdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKQ0KICAgICAgICAgIC52YWx1ZQ0KDQogICAgICAgIC8vIOabtOaWsOihjOaVsOaNrg0KICAgICAgICByb3cuc3VidG90YWwgPSBjdXJyZW5jeShzdWJ0b3RhbCwge3ByZWNpc2lvbjogMn0pLnZhbHVlDQogICAgICAgIHJvdy5zcWREbkN1cnJlbmN5QmFsYW5jZSA9IHJvdy5pc0FjY291bnRDb25maXJtZWQgPT09ICIwIiA/IGN1cnJlbmN5KHN1YnRvdGFsLCB7cHJlY2lzaW9uOiAyfSkudmFsdWUgOiByb3cuc3FkRG5DdXJyZW5jeUJhbGFuY2UNCg0KICAgICAgICAvLyDmoLnmja7kuI3lkIznmoTovpPlhaXnsbvlnovlhbPpl63lr7nlupTnmoTnvJbovpHnirbmgIENCiAgICAgICAgc3dpdGNoIChjYXRlZ29yeSkgew0KICAgICAgICAgIGNhc2UgInN0cmF0ZWd5IjoNCiAgICAgICAgICAgIHJvdy5zaG93U3RyYXRlZ3kgPSBmYWxzZQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJ1bml0UmF0ZSI6DQogICAgICAgICAgICAvLyDkuI3lnKjov5nph4zlhbPpl63nvJbovpHnirbmgIEs5pS555SoQGJsdXLkuovku7YNCiAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgY2FzZSAiYW1vdW50IjoNCiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tg0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJjdXJyZW5jeVJhdGUiOg0KICAgICAgICAgICAgLy8g5LiN5Zyo6L+Z6YeM5YWz6Zet57yW6L6R54q25oCBLOaUueeUqEBibHVy5LqL5Lu2DQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIGNhc2UgImR1dHlSYXRlIjoNCiAgICAgICAgICAgIC8vIOS4jeWcqOi/memHjOWFs+mXree8lui+keeKtuaAgSzmlLnnlKhAYmx1cuS6i+S7tg0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgIC8vIOinpuWPkeaVsOaNruabtOaWsA0KICAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgdGhpcy5jaGFyZ2VEYXRhIHx8IFtdKQ0KICAgICAgICAgDQogICAgICAgICAvLyDmlbDmja7mm7TmlrDlkI7liLfmlrDooajmoLwNCiAgICAgICAgIHRoaXMucmVmcmVzaFRhYmxlKCkNCg0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6K6h566X5bCP6K6h5pe25Ye66ZSZOiIsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLorqHnrpflsI/orqHml7blh7rplJks6K+35qOA5p+l6L6T5YWl5YC85piv5ZCm5q2j56GuIikNCiAgICAgIH0NCiAgICB9LA0KICAgIGRlbGV0ZUl0ZW0ocm93KSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVJdGVtIiwgcm93KQ0KICAgIH0sDQogICAgZGVsZXRlQWxsSXRlbShyb3cpIHsNCiAgICAgIHRoaXMuJGVtaXQoImRlbGV0ZUFsbCIpDQogICAgfSwNCiAgICAgICAgIGNvbXBhbnlOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgICByZXR1cm4gew0KICAgICAgICAgaWQ6IG5vZGUuY29tcGFueUlkLA0KICAgICAgICAgbGFiZWw6IChub2RlLmNvbXBhbnlTaG9ydE5hbWUgIT0gbnVsbCA/IG5vZGUuY29tcGFueVNob3J0TmFtZSA6ICIiKSArICIgIiArIChub2RlLmNvbXBhbnlMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY29tcGFueUxvY2FsTmFtZSA6ICIiKSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMoKG5vZGUuY29tcGFueVNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55U2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55TG9jYWxOYW1lIDogIiIpKQ0KICAgICAgIH0NCiAgICAgfSwNCiAgICAgLy8g5Yi35paw6KGo5qC85biD5bGADQogICAgIHJlZnJlc2hUYWJsZSgpIHsNCiAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICBpZiAodGhpcy4kcmVmcy5jaGFyZ2VUYWJsZSkgew0KICAgICAgICAgICB0aGlzLiRyZWZzLmNoYXJnZVRhYmxlLmRvTGF5b3V0KCkNCiAgICAgICAgIH0NCiAgICAgICB9KQ0KICAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["debitNoteChargeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiOA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debitNoteChargeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-col :style=\"{'display':openChargeList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n    <div :class=\"{'inactive':openChargeList==false,'active':openChargeList}\">\r\n      <el-table\r\n        ref=\"chargeTable\"\r\n        :data=\"chargeData\"\r\n        border\r\n        class=\"pd0\"\r\n        row-key=\"getItemKey\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        :row-class-name=\"setRowData\"\r\n      >\r\n        <el-table-column\r\n          align=\"center\"\r\n          type=\"selection\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCharge\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true\"\r\n            >\r\n              {{ scope.row.chargeName }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCharge\" :dbn=\"true\" :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"scope.row.dnChargeNameId\" :placeholder=\"'运费'\"\r\n                         :type=\"'charge'\"\r\n                         @return=\"scope.row.dnChargeNameId = $event\"\r\n                         @returnData=\"handleChargeSelect(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true\"\r\n            >\r\n              {{ scope.row.dnCurrencyCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationCurrency\" :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                         :pass=\"scope.row.dnCurrencyCode\" :type=\"'currency'\"\r\n                         @return=\"changeCurrency(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"80px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showUnitRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true\"\r\n            >\r\n              {{\r\n                scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"¥\"\r\n                }).format() : (scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: \"$\"\r\n                }).format() : scope.row.dnUnitRate)\r\n              }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showUnitRate\" v-model=\"scope.row.dnUnitRate\" :controls=\"false\"\r\n                             :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\"\r\n                             :precision=\"4\" style=\"display:flex;width: 100%\"\r\n                             @blur=\"scope.row.showUnitRate=false\"\r\n                             @change=\"countProfit(scope.row,'unitRate')\"\r\n                             @input=\"countProfit(scope.row,'unitRate')\"\r\n                             @focusout.native=\"scope.row.showUnitRate=false\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单位\" prop=\"quotationUnitId\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showQuotationUnit\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true\"\r\n            >\r\n              {{ scope.row.dnUnitCode }}\r\n            </div>\r\n            <tree-select v-show=\"scope.row.showQuotationUnit\"\r\n                         :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\" :pass=\"scope.row.dnUnitCode\"\r\n                         :type=\"'unit'\" @return=\"changeUnit(scope.row,$event)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"!scope.row.showAmount\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true\"\r\n            >\r\n              {{ scope.row.dnAmount }}\r\n            </div>\r\n            <el-input-number v-if=\"scope.row.showAmount\" v-model=\"scope.row.dnAmount\" :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.00\" placeholder=\"数量\"\r\n                             style=\"display:flex;width: 100%\" @blur=\"scope.row.showAmount=false\"\r\n                             @change=\"countProfit(scope.row,'amount')\"\r\n                             @input=\"countProfit(scope.row,'amount')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n          <template slot-scope=\"scope\" style=\"display:flex;\">\r\n            <div v-if=\"!scope.row.showCurrencyRate\"\r\n                 @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true\"\r\n            >\r\n              {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}\r\n            </div>\r\n            <el-input-number v-show=\"scope.row.showCurrencyRate\" v-model=\"scope.row.basicCurrencyRate\"\r\n                             :controls=\"false\"\r\n                             :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                             :min=\"0.0001\" :precision=\"4\" :step=\"0.0001\"\r\n                             style=\"width: 100%\" @blur=\"scope.row.showCurrencyRate=false\"\r\n                             @change=\"countProfit(scope.row,'currencyRate')\"\r\n                             @input=\"countProfit(scope.row,'currencyRate')\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"50px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"display: flex;justify-content: center\">\r\n              <div v-if=\"!scope.row.showDutyRate\"\r\n                   @click=\"(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true\"\r\n              >\r\n                {{ scope.row.dutyRate }}\r\n              </div>\r\n              <el-input-number v-if=\"scope.row.showDutyRate\" v-model=\"scope.row.dutyRate\" :controls=\"false\"\r\n                               :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n                               :min=\"0\" style=\"width: 75%\"\r\n                               @blur=\"scope.row.showDutyRate=false\"\r\n                               @change=\"countProfit(scope.row,'dutyRate')\"\r\n                               @input=\"countProfit(scope.row,'dutyRate')\"\r\n              />\r\n              <div>%</div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"小计\" prop=\"subtotal\" width=\"75\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{\r\n                currency(scope.row.subtotal, {\r\n                  separator: \",\",\r\n                  precision: 2,\r\n                  symbol: (scope.row.dnCurrencyCode === \"RMB\" ? \"¥\" : \"$\")\r\n                }).format()\r\n              }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"费用备注\">\r\n          <template slot-scope=\"scope\">\r\n            <input v-model=\"scope.row.chargeRemark\"\r\n                   :disabled=\"disabled|| scope.row.isAccountConfirmed == '1'\"\r\n                   style=\"border: none;width: 100%;height: 100%;\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"审核状态\">\r\n          <template slot-scope=\"scope\">\r\n            {{ auditStatus(scope.row.isAccountConfirmed) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"已收金额\">\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.sqdDnCurrencyPaid\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"未收余额\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属服务\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot=\"header\" slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || hasConfirmRow\"\r\n              size=\"mini\"\r\n              style=\"color: red\"\r\n              type=\"text\"\r\n              @click=\"deleteAllItem()\"\r\n            >全部删除\r\n            </el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              :disabled=\"disabled || scope.row.isAccountConfirmed == '1'\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteItem(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <!-- 空数据提示 -->\r\n        <template slot=\"empty\">\r\n          <div style=\"padding: 20px; text-align: center; color: #909399; display: flex; flex-direction: column; align-items: center;\">\r\n            <i class=\"el-icon-document\" style=\"font-size: 24px; margin-bottom: 10px;\"></i>\r\n            <p>暂无费用数据</p>\r\n          </div>\r\n        </template>\r\n      </el-table>\r\n    </div>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport Sortable from \"sortablejs\"\r\n\r\nexport default {\r\n  name: \"debitNoteChargeList\",\r\n  components: {CompanySelect, Treeselect},\r\n  props: [\"chargeData\", \"companyList\", \"openChargeList\", \"isReceivable\", \"disabled\",\r\n    \"hiddenSupplier\", \"rsClientMessageReceivableTaxUSD\",\r\n    \"rsClientMessageReceivableTaxRMB\", \"rsClientMessagePayableTaxUSD\", \"rsClientMessagePayableTaxRMB\",\r\n    \"rsClientMessageReceivableRMB\", \"rsClientMessageReceivableUSD\", \"rsClientMessagePayableRMB\",\r\n    \"rsClientMessagePayableUSD\", \"rsClientMessageProfit\", \"rsClientMessageProfitNoTax\", \"payDetailRMB\",\r\n    \"payDetailUSD\", \"payDetailRMBTax\", \"payDetailUSDTax\", \"rsClientMessageProfitUSD\", \"rsClientMessageProfitRMB\",\r\n    \"rsClientMessageProfitTaxRMB\", \"rsClientMessageProfitTaxUSD\", \"debitNote\", \"dragGroupName\"],\r\n  computed: {\r\n    hasConfirmRow() {\r\n      let result = false;\r\n      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {\r\n        if (item.isAccountConfirmed === \"1\") {\r\n          result = true\r\n        }\r\n      }) : null\r\n      return result\r\n    }\r\n  },\r\n  watch: {\r\n    chargeData: {\r\n      handler: function (newVal, oldVal) {\r\n        if (!oldVal) {\r\n          this.$emit(\"return\", newVal)\r\n          return\r\n        }\r\n\r\n        // 遍历费用列表，检查币种变化\r\n        newVal ? newVal.forEach((item, index) => {\r\n          const oldItem = oldVal[index]\r\n\r\n          // 检查币种变化并计算小计\r\n          if (item.currency && item.amount) {\r\n            // 如果从 RMB 换成 USD，使用 1/汇率 计算\r\n            if (oldItem && oldItem.currency === \"RMB\" && item.currency === \"USD\") {\r\n              if (item.exchangeRate && item.exchangeRate !== 0) {\r\n                try {\r\n                  // 计算 1/汇率，保留4位小数\r\n                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value\r\n\r\n                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)\r\n                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})\r\n                    .multiply(item.amount)\r\n                    .multiply(inverseRate)\r\n                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))\r\n                    .value\r\n                } catch (error) {\r\n                  console.error(\"计算小计出错:\", error)\r\n                  item.subtotal = 0\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }) : null\r\n\r\n        this.$emit(\"return\", newVal ? newVal : [])\r\n\r\n        // 数据变化后重新初始化拖拽，使用更安全的方式\r\n        this.reinitializeSortable()\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    },\r\n    disabled: {\r\n      handler: function (newVal) {\r\n        // 禁用状态改变时重新初始化拖拽\r\n        this.reinitializeSortable()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initSortable()\r\n  },\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.reinitTimer) {\r\n      clearTimeout(this.reinitTimer)\r\n      this.reinitTimer = null\r\n    }\r\n    // 销毁拖拽实例\r\n    this.destroySortable()\r\n  },\r\n  data() {\r\n    return {\r\n      payTotalRMB: 0,\r\n      payTotalUSD: 0,\r\n      showClientName: null,\r\n      sortable: null,\r\n      reinitTimer: null,\r\n      dragStartColumn: -1, // 记录拖拽开始的列索引\r\n      services: [{\r\n        value: 1,\r\n        label: \"海运\"\r\n      }, {\r\n        value: 10,\r\n        label: \"空运\"\r\n      }, {\r\n        value: 20,\r\n        label: \"铁路\"\r\n      }, {\r\n        value: 40,\r\n        label: \"快递\"\r\n      }, {\r\n        value: 50,\r\n        label: \"拖车\"\r\n      }, {\r\n        value: 60,\r\n        label: \"报关\"\r\n      }, {\r\n        value: 70,\r\n        label: \"清关派送\"\r\n      }, {\r\n        value: 80,\r\n        label: \"码头仓储\"\r\n      }, {\r\n        value: 90,\r\n        label: \"检验证书\"\r\n      }, {\r\n        value: 100,\r\n        label: \"保险\"\r\n      }, {\r\n        value: 101,\r\n        label: \"扩展服务\"\r\n      }],\r\n      service: [{\r\n        value: 1,\r\n        label: \"基础服务\"\r\n      }, {\r\n        value: 4,\r\n        label: \"前程运输\"\r\n      }, {\r\n        value: 5,\r\n        label: \"出口报关\"\r\n      }, {\r\n        value: 6,\r\n        label: \"进口清关\"\r\n      }, {value: 2, label: \"海运\"}\r\n        , {value: 3, label: \"陆运\"}\r\n        , {value: 4, label: \"铁路\"}\r\n        , {value: 5, label: \"空运\"}\r\n        , {value: 6, label: \"快递\"}\r\n        , {value: 21, label: \"整柜海运\"}\r\n        , {value: 22, label: \"拼柜海运\"}\r\n        , {value: 23, label: \"散杂船\"}\r\n        , {value: 24, label: \"滚装船\"}\r\n        , {value: 41, label: \"整柜铁路\"}\r\n        , {value: 42, label: \"拼柜铁路\"}\r\n        , {value: 43, label: \"铁路车皮\"}\r\n        , {value: 51, label: \"空运普舱\"}\r\n        , {value: 52, label: \"空运包板\"}\r\n        , {value: 53, label: \"空运包机\"}\r\n        , {value: 54, label: \"空运行李\"}\r\n        , {value: 961, label: \"前程运输\"}\r\n        , {value: 964, label: \"进口清关\"}\r\n        , {value: 7, label: \"出口报关\"}\r\n      ],\r\n      chargeRemark: null\r\n    }\r\n  },\r\n  methods: {\r\n    destroySortable() {\r\n      if (this.sortable) {\r\n        try {\r\n          // 检查 sortable 实例是否还有效\r\n          if (this.sortable.el && this.sortable.el.parentNode) {\r\n            this.sortable.destroy()\r\n          }\r\n        } catch (error) {\r\n          console.warn('Error destroying sortable:', error)\r\n        } finally {\r\n          this.sortable = null\r\n        }\r\n      }\r\n    },\r\n    reinitializeSortable() {\r\n      // 使用防抖延迟重新初始化，避免频繁的创建销毁\r\n      if (this.reinitTimer) {\r\n        clearTimeout(this.reinitTimer)\r\n      }\r\n\r\n      this.reinitTimer = setTimeout(() => {\r\n        this.$nextTick(() => {\r\n          this.destroySortable()\r\n          // 确保 DOM 已更新后再初始化\r\n          this.$nextTick(() => {\r\n            this.initSortable()\r\n          })\r\n        })\r\n      }, 100)\r\n    },\r\n    initSortable() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.chargeTable && this.$refs.chargeTable.$el) {\r\n          const tbody = this.$refs.chargeTable.$el.querySelector('tbody')\r\n          if (tbody) {\r\n            try {\r\n              this.sortable = Sortable.create(tbody, {\r\n                group: {\r\n                  name: this.dragGroupName || 'debitNoteChargeGroup',\r\n                  pull: (to, from, dragEl, evt) => {\r\n                    // 根据拖拽列决定是否允许拖出（剪切或复制）\r\n                    if (this.dragStartColumn === 1) {\r\n                      return true; // 从第一列拖动时允许拖出（剪切）\r\n                    } else {\r\n                      return 'clone'; // 从其他列拖动时复制\r\n                    }\r\n                  },\r\n                  put: !this.disabled\r\n                },\r\n                animation: 150,\r\n                disabled: this.disabled,\r\n                ghostClass: 'sortable-ghost',\r\n                dragClass: 'sortable-drag',\r\n                filter: '.disabled',\r\n                onStart: (evt) => {\r\n                  // 拖拽开始\r\n                  if (!this.chargeData || !this.chargeData.length) return\r\n                  const draggedItem = this.chargeData[evt.oldIndex]\r\n\r\n                  // 获取拖拽开始的列索引\r\n                  this.dragStartColumn = -1 // 默认设置为-1\r\n                  try {\r\n                    // 获取拖拽事件的起始坐标\r\n                    const mouseX = evt.originalEvent.clientX\r\n                    const mouseY = evt.originalEvent.clientY\r\n\r\n                    // 尝试更准确地确定拖拽开始的列\r\n                    const cells = evt.item.querySelectorAll('td')\r\n                    if (cells && cells.length > 0) {\r\n                      // 计算每个单元格的位置，找到包含鼠标位置的单元格\r\n                      for (let i = 0; i < cells.length; i++) {\r\n                        const rect = cells[i].getBoundingClientRect()\r\n                        if (mouseX >= rect.left && mouseX <= rect.right &&\r\n                          mouseY >= rect.top && mouseY <= rect.bottom) {\r\n                          this.dragStartColumn = i\r\n                          break\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    // 备选方法：如果上面的方法没找到，使用表头定位\r\n                    if (this.dragStartColumn === -1) {\r\n                      const headerCells = this.$refs.chargeTable.$el.querySelectorAll('thead th')\r\n                      if (headerCells && headerCells.length > 0) {\r\n                        for (let i = 0; i < headerCells.length; i++) {\r\n                          const rect = headerCells[i].getBoundingClientRect()\r\n                          if (mouseX >= rect.left && mouseX <= rect.right) {\r\n                            this.dragStartColumn = i\r\n                            break\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n\r\n                    if (this.dragStartColumn === -1) {\r\n                      // 回退方案：如果通过坐标无法确定，则默认为非第一列\r\n                      this.dragStartColumn = 2 // 设置为非第一列，默认为复制模式\r\n                    }\r\n\r\n                  } catch (error) {\r\n                    console.error('确定拖拽开始列时出错:', error)\r\n                    this.dragStartColumn = 1 // 出错时默认为第一列\r\n                  }\r\n\r\n                  // 设置被拖拽元素的数据\r\n                  try {\r\n                    evt.item.setAttribute('data-drag-item', JSON.stringify(draggedItem))\r\n                    // 额外添加拖拽起始列信息\r\n                    evt.item.setAttribute('data-drag-column', this.dragStartColumn)\r\n                  } catch (error) {\r\n                    console.error('Failed to stringify drag item:', error)\r\n                    evt.item.setAttribute('data-drag-item', '{}')\r\n                    evt.item.setAttribute('data-drag-column', '-1')\r\n                  }\r\n\r\n                  this.$emit('dragStart', {\r\n                    item: draggedItem,\r\n                    index: evt.oldIndex,\r\n                    from: this,\r\n                    column: this.dragStartColumn\r\n                  })\r\n                },\r\n                onAdd: (evt) => {\r\n                  // 接收到新元素\r\n                  if (!this.chargeData) return\r\n                  const item = evt.item\r\n                  let draggedItem = {}\r\n                  let dragStartColumn = -1\r\n\r\n                  try {\r\n                    const dragData = item.getAttribute('data-drag-item')\r\n                    if (dragData && dragData !== 'undefined') {\r\n                      draggedItem = JSON.parse(dragData)\r\n                    }\r\n\r\n                    // 获取拖拽起始列\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag item data:', error)\r\n                  }\r\n\r\n                  // 处理新增元素到表格\r\n                  const newChargeData = [...this.chargeData]\r\n\r\n                  // 无论是复制还是剪切，都需要添加项到目标位置\r\n                  // 但要给新项生成一个新的ID，表示这是一个全新的项\r\n                  newChargeData.splice(evt.newIndex, 0, {\r\n                    ...draggedItem,\r\n                    tempId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\r\n                    chargeId: null // 清除原有ID，作为新增项\r\n                  })\r\n\r\n                  this.$emit('return', newChargeData)\r\n                                     this.$emit('dragAdd', {\r\n                     item: draggedItem,\r\n                     newIndex: evt.newIndex,\r\n                     to: this,\r\n                     column: dragStartColumn\r\n                   })\r\n                   \r\n                   // 拖拽添加后刷新表格\r\n                   this.refreshTable()\r\n                },\r\n                onRemove: (evt) => {\r\n                  // 获取拖拽信息\r\n                  if (!this.chargeData) return\r\n                  const item = evt.item\r\n                  let dragStartColumn = -1\r\n                  try {\r\n                    const columnData = item.getAttribute('data-drag-column')\r\n                    if (columnData && columnData !== 'undefined') {\r\n                      dragStartColumn = parseInt(columnData, 10)\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('Failed to parse drag column data:', error)\r\n                  }\r\n\r\n                  const newChargeData = [...this.chargeData]\r\n                  const removedItem = newChargeData[evt.oldIndex]\r\n\r\n                  // 只有在从第一列开始拖拽时才执行剪切操作\r\n                  // Sortable的clone选项已经控制了复制行为，这里我们只需处理剪切的情况\r\n                  if (dragStartColumn === 1) {\r\n                    newChargeData.splice(evt.oldIndex, 1)\r\n                    this.$emit('return', newChargeData)\r\n                  }\r\n\r\n                                     this.$emit('dragRemove', {\r\n                     item: removedItem,\r\n                     oldIndex: evt.oldIndex,\r\n                     from: this,\r\n                     column: dragStartColumn,\r\n                     isCut: dragStartColumn === 1\r\n                   })\r\n                   \r\n                   // 拖拽移除后刷新表格\r\n                   this.refreshTable()\r\n                },\r\n                onUpdate: (evt) => {\r\n                  // 同一表格内排序\r\n                  if (!this.chargeData) return\r\n                  const newChargeData = [...this.chargeData]\r\n                  const item = newChargeData.splice(evt.oldIndex, 1)[0]\r\n                  newChargeData.splice(evt.newIndex, 0, item)\r\n\r\n                  this.$emit('return', newChargeData)\r\n                                     this.$emit('dragUpdate', {\r\n                     item: item,\r\n                     oldIndex: evt.oldIndex,\r\n                     newIndex: evt.newIndex,\r\n                     column: this.dragStartColumn\r\n                   })\r\n                   \r\n                   // 拖拽更新后刷新表格\r\n                   this.refreshTable()\r\n                },\r\n                                 onEnd: (evt) => {\r\n                   // 拖拽结束\r\n                   this.$emit('dragEnd', {\r\n                     ...evt,\r\n                     dragColumn: this.dragStartColumn\r\n                   })\r\n                   // 重置拖拽列\r\n                   this.dragStartColumn = -1\r\n                   \r\n                   // 拖拽结束后刷新表格\r\n                   this.refreshTable()\r\n                 }\r\n              })\r\n            } catch (error) {\r\n              console.error('Error creating sortable:', error)\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    setRowData({row, rowIndex}) {\r\n      // 为每行设置数据属性，用于拖拽传递数据\r\n      this.$nextTick(() => {\r\n        const tableRows = this.$refs.chargeTable.$el.querySelectorAll('tbody tr')\r\n        if (tableRows[rowIndex]) {\r\n          try {\r\n            tableRows[rowIndex].setAttribute('data-drag-item', JSON.stringify(row))\r\n          } catch (error) {\r\n            console.error('Failed to stringify row data:', error)\r\n            tableRows[rowIndex].setAttribute('data-drag-item', '{}')\r\n          }\r\n        }\r\n      })\r\n      return ''\r\n    },\r\n    auditStatus(status) {\r\n      return status == 1 ? \"已审核\" : \"未审核\"\r\n    },\r\n    selectCharge(target, row) {\r\n      row.dnChargeNameId = target.chargeId\r\n      row.chargeName = target.chargeLocalName\r\n    },\r\n    handleSelectionChange(val) {\r\n      // 处理选择变化\r\n      this.$emit(\"selectRow\", val)\r\n\r\n      this.payTotalUSD = 0\r\n      this.payTotalRMB = 0\r\n      val ? val.map(item => {\r\n        if (item.isRecievingOrPaying == 1) {\r\n          if (item.dnCurrencyCode === \"USD\") {\r\n            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)\r\n          } else {\r\n            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)\r\n          }\r\n\r\n        }\r\n      }) : null\r\n\r\n    },\r\n    currency,\r\n    // 获取项目的唯一键\r\n    getItemKey(item) {\r\n      return item.tempId || item.chargeId || item.id || `item_${Math.random().toString(36).substr(2, 9)}`\r\n    },\r\n    getServiceName(id) {\r\n      let serviceName = \"\"\r\n      this.services.map(obj => {\r\n        obj.value === id ? serviceName = obj.label : null\r\n      })\r\n      return serviceName\r\n    },\r\n    copyFreight(row) {\r\n      if (this.companyList.length > 0) {\r\n        row.payClearingCompanyId = this.companyList[0].companyId\r\n        row.payCompanyName = this.companyList[0].companyShortName\r\n      }\r\n      row.isAccountConfirmed = 0\r\n      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息\r\n      let data = this._.cloneDeep(row)\r\n\r\n      this.$emit(\"copyFreight\", {...data, chargeId: null})\r\n    },\r\n    copyAllFreight() {\r\n      if (!this.companyList.length > 0) {\r\n        this.$modal.alertWarning(\"请先选择委托单位或关联单位\")\r\n        return\r\n      }\r\n\r\n      if (!this.chargeData || !this.chargeData.length) {\r\n        this.$modal.alertWarning(\"暂无费用数据可复制\")\r\n        return\r\n      }\r\n\r\n      this.chargeData.map(charge => {\r\n        charge.payClearingCompanyId = this.companyList[0].companyId\r\n        charge.payCompanyName = this.companyList[0].companyShortName\r\n        charge.isRecievingOrPaying = 0\r\n        charge.isAccountConfirmed = 0\r\n        charge.chargeId = null\r\n        this.$emit(\"copyFreight\", this._.cloneDeep(charge))\r\n      })\r\n    },\r\n    changeUnitCost(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showCostUnit = false\r\n      })\r\n    },\r\n    changeUnit(row, unit) {\r\n      row.dnUnitCode = unit\r\n      this.$nextTick(() => {\r\n        row.showQuotationUnit = false\r\n      })\r\n    },\r\n    handleChargeSelect(row, data) {\r\n      if (row.chargeLocalName === data.chargeName) {\r\n        row.chargeName = data.chargeLocalName\r\n        row.showQuotationCharge = false\r\n      }\r\n      if (row.currencyCode == null && data.currencyCode) {\r\n        row.dnCurrencyCode = data.currencyCode\r\n      }\r\n    },\r\n    changeCurrency(row, currencyCode) {\r\n      row.dnCurrencyCode = currencyCode\r\n      /* let exchangeRate\r\n      if (currencyCode === \"USD\") {\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && row.dnCurrencyCode == a.overseaCurrency\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      } */\r\n\r\n      this.$nextTick(() => {\r\n        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1\r\n        row.showQuotationCurrency = false\r\n      })\r\n    },\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      // 确保 chargeData 存在\r\n      if (!this.chargeData) {\r\n        this.$emit(\"return\", [])\r\n        return\r\n      }\r\n\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true,\r\n        showStrategy: true,\r\n        showUnitRate: true,\r\n        showAmount: true,\r\n        showCurrencyRate: true,\r\n        showDutyRate: true,\r\n        basicCurrencyRate: 1,\r\n        dutyRate: 0,\r\n        dnAmount: 1,\r\n        // 应收还是应付\r\n        isRecievingOrPaying: this.isReceivable ? 0 : 1,\r\n        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null\r\n      }\r\n      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1\r\n      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10\r\n      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20\r\n      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40\r\n      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50\r\n      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60\r\n      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70\r\n      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80\r\n      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90\r\n      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100\r\n             if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101\r\n       this.chargeData.push(obj)\r\n       \r\n       // 添加新项目后刷新表格\r\n       this.refreshTable()\r\n    },\r\n    countProfit(row, category) {\r\n      // 确保所有必要的值都存在且有效\r\n      if (!row) return\r\n\r\n      // 使用currency.js来处理数值,避免精度损失\r\n      const unitRate = row.dnUnitRate || 0\r\n      const amount = row.dnAmount || 0\r\n      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value\r\n      const dutyRate = currency(row.dutyRate || 0).value\r\n\r\n      try {\r\n        // 计算小计\r\n        const subtotal = currency(unitRate, {precision: 4})\r\n          .multiply(amount)\r\n          .multiply(currencyRate)\r\n          .multiply(currency(1).add(currency(dutyRate).divide(100)))\r\n          .value\r\n\r\n        // 更新行数据\r\n        row.subtotal = currency(subtotal, {precision: 2}).value\r\n        row.sqdDnCurrencyBalance = row.isAccountConfirmed === \"0\" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance\r\n\r\n        // 根据不同的输入类型关闭对应的编辑状态\r\n        switch (category) {\r\n          case \"strategy\":\r\n            row.showStrategy = false\r\n            break\r\n          case \"unitRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"amount\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"currencyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n          case \"dutyRate\":\r\n            // 不在这里关闭编辑状态,改用@blur事件\r\n            break\r\n        }\r\n\r\n                 // 触发数据更新\r\n         this.$emit(\"return\", this.chargeData || [])\r\n         \r\n         // 数据更新后刷新表格\r\n         this.refreshTable()\r\n\r\n      } catch (error) {\r\n        console.error(\"计算小计时出错:\", error)\r\n        this.$message.error(\"计算小计时出错,请检查输入值是否正确\")\r\n      }\r\n    },\r\n    deleteItem(row) {\r\n      this.$emit(\"deleteItem\", row)\r\n    },\r\n    deleteAllItem(row) {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n         companyNormalizer(node) {\r\n       return {\r\n         id: node.companyId,\r\n         label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n       }\r\n     },\r\n     // 刷新表格布局\r\n     refreshTable() {\r\n       this.$nextTick(() => {\r\n         if (this.$refs.chargeTable) {\r\n           this.$refs.chargeTable.doLayout()\r\n         }\r\n       })\r\n     }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 拖拽样式\r\n::v-deep .sortable-ghost {\r\n  opacity: 0.5;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n::v-deep .sortable-drag {\r\n  opacity: 0.8;\r\n  background-color: #ecf5ff;\r\n  border: 1px dashed #409eff;\r\n}\r\n\r\n// 拖拽时的表格行样式\r\n::v-deep tbody tr {\r\n  cursor: move;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n::v-deep tbody tr:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n// 禁用状态下不显示拖拽光标\r\n::v-deep tbody tr.disabled {\r\n  cursor: default;\r\n}\r\n</style>\r\n"]}]}