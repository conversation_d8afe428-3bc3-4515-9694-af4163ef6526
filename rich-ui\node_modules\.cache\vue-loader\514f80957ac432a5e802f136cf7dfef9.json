{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue?vue&type=template&id=1a95385c&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue", "mtime": 1754903721862}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}