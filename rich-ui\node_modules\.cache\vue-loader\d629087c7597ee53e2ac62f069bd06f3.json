{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue?vue&type=style&index=0&id=0513f50e&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue", "mtime": 1754903425617}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5leHBhbmQtY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOWY5Zjk7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgbWFyZ2luOiAxMHB4IDA7DQp9DQoNCi5jaGFyZ2UtbGlzdC1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmctYm90dG9tOiAxMHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsNCg0KICBzcGFuIHsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgY29sb3I6ICMzMDMxMzM7DQogIH0NCn0NCg0KaW5wdXQ6Zm9jdXMgew0KICBvdXRsaW5lOiBub25lOw0KfQ0KDQoudW5IaWdobGlnaHQtdGV4dCB7DQogIGNvbG9yOiAjYjdiYmMyOw0KICBtYXJnaW46IDA7DQp9DQoNCi8vIOimhuebliBFbGVtZW50IFVJIOihqOagvOagt+W8jw0KOmRlZXAoLmVsLXRhYmxlKSB7DQogIC5lbC10YWJsZV9fZXhwYW5kZWQtY2VsbCB7DQogICAgcGFkZGluZzogMDsNCg0KICAgIC5leHBhbmQtY29udGVudCB7DQogICAgICBtYXJnaW46IDA7DQogICAgICBib3JkZXI6IG5vbmU7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["debitNodeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmwBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debitNodeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <div class=\"debit-note-list\">\r\n    <el-table\r\n      ref=\"debitNoteTable\"\r\n      :data=\"debitNoteList\"\r\n      border\r\n      style=\"width: 100%\"\r\n      @expand-change=\"handleExpandChange\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <!-- 可展开列 -->\r\n      <el-table-column type=\"expand\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- 嵌套 chargeList 组件 -->\r\n          <debit-note-charge-list\r\n            :charge-data=\"scope.row.rsChargeList\"\r\n            :company-list=\"companyList\"\r\n            :debit-note=\"scope.row\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            :hidden-supplier=\"hiddenSupplier\"\r\n            :is-receivable=\"isReceivable\"\r\n            :open-charge-list=\"true\"\r\n            @copyFreight=\"handleCopyFreight\"\r\n            @deleteAll=\"handleDeleteAll\"\r\n            @deleteItem=\"scope.row.rsChargeList = scope.row.rsChargeList.filter(charge => charge !== $event)\"\r\n            @return=\"handleChargeDataChange(scope.row, $event)\"\r\n            @selectRow=\"handleChargeSelection(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- 勾选列 -->\r\n      <el-table-column align=\"center\" type=\"selection\"></el-table-column>\r\n\r\n      <!-- 分账单基本信息列 -->\r\n      <el-table-column label=\"所属公司\" prop=\"sqdRctNo\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.companyBelongsTo\" :placeholder=\"'收付路径'\"\r\n                       :type=\"'rsPaymentTitle'\" @return=\"scope.row.companyBelongsTo=$event\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"我司账户\" prop=\"companyName\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.bankAccountCode\" :placeholder=\"'我司账户'\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       :type=\"'companyAccount'\"\r\n                       @return=\"scope.row.bankAccountCode=$event\" @returnData=\"selectBankAccount(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"收付标志\" prop=\"isRecievingOrPaying\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ scope.row.isRecievingOrPaying == 0 ? \"收\" : \"付\" }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算单位\" prop=\"dnCurrencyCode\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :custom-options=\"companyList\" :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                       :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       @returnData=\"handleSelectCompany(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"对方账户\" prop=\"billReceivable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input v-model=\"scope.row.clearingCompanyBankAccount\"\r\n                    :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n                    :disabled=\"debitNoteDisabled(scope.row)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算币种\" prop=\"billPayable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       :pass=\"scope.row.dnCurrencyCode\"\r\n                       :type=\"'currency'\"\r\n                       @return=\"changeCurrency(scope.row,$event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"right\" label=\"账单应收\" v-if=\"isReceivable\" prop=\"billReceivable\" width=\"80\"/>\r\n      <el-table-column align=\"right\" label=\"账单应付\" v-if=\"!isReceivable\" prop=\"billPayable\" width=\"80\"/>\r\n\r\n      <el-table-column align=\"center\" label=\"账单状态\" prop=\"billStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          >\r\n            {{ getBillStatusText(scope.row.billStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getInvoiceStatusType(scope.row.invoiceStatus)\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n            size=\"mini\" @click=\"handleInvoiceStatusClick(scope.row)\"\r\n          >\r\n            {{ getInvoiceStatusText(scope.row.invoiceStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"申请支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.requestPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预计支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.expectedPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.actualPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"销账状态\" prop=\"writeoffStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getWriteoffStatusType(scope.row.writeoffStatus)\"\r\n            size=\"mini\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          >\r\n            {{ getWriteoffStatusText(scope.row.writeoffStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column class-name=\"small-padding fixed-width\" fixed=\"right\" label=\"操作\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <div style=\"display: flex; gap: 4px;\">\r\n            <el-button\r\n              v-if=\"scope.row.billStatus==='confirmed'\"\r\n              icon=\"el-icon-unlock\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"applyUnlock(scope.row)\"\r\n            >\r\n              申请解锁\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"scope.row.billStatus==='draft'\"\r\n              icon=\"el-icon-check\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"setComplete(scope.row)\"\r\n            >\r\n              设置完成\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"scope.row.rsChargeList.length>0\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteDebitNote(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addDebitNote\"\r\n    >[＋]\r\n    </el-button>\r\n\r\n    <!-- 发票对话框 -->\r\n    <vatinvoice-dialog\r\n      :company-list=\"companyList\"\r\n      :form=\"invoiceForm\"\r\n      :invoice-items=\"invoiceItems\"\r\n      :title=\"'增值税发票管理'\"\r\n      :visible.sync=\"invoiceDialogVisible\"\r\n      @cancel=\"handleInvoiceCancel\"\r\n      @submit=\"handleInvoiceSubmit\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport debitNoteChargeList from \"@/views/system/document/debitNoteChargeList.vue\"\r\nimport VatinvoiceDialog from \"@/views/system/vatinvoice/components/VatinvoiceDialog.vue\"\r\nimport {\r\n  addVatinvoice,\r\n  updateVatinvoice,\r\n  getVatinvoice,\r\n  countVatinvoiceByRctId,\r\n  generateInvoiceCode\r\n} from \"@/api/system/vatInvoice\"\r\nimport {updateDebitNote} from \"@/api/system/debitnote\"\r\n\r\nexport default {\r\n  name: \"debitNoteList\",\r\n  components: {debitNoteChargeList, VatinvoiceDialog},\r\n  props: [\r\n    \"companyList\",\r\n    \"disabled\",\r\n    \"hiddenSupplier\",\r\n    \"rctId\",\r\n    \"debitNoteList\",\r\n    \"isReceivable\"\r\n  ],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      expandedRows: [],\r\n      localDebitNoteList: [],\r\n      selectedDebitNotes: [],\r\n      invoiceDialogVisible: false,\r\n      currentDebitNote: null,\r\n      invoiceForm: {},\r\n      invoiceItems: []\r\n    }\r\n  },\r\n  watch: {\r\n    debitNoteList: {\r\n      immediate: true,\r\n      deep: true,\r\n      handler(newVal) {\r\n        this.$emit(\"update:debitNoteList\", newVal)\r\n        this.$emit(\"return\", newVal)\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  methods: {\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedDebitNotes = selection\r\n      this.$emit(\"selection-change\", selection)\r\n    },\r\n    debitNoteDisabled(row) {\r\n      return row.billStatus === \"confirmed\" || this.disabled\r\n    },\r\n    async handleInvoiceStatusClick(row) {\r\n      // 申请开票\r\n      this.currentDebitNote = row\r\n\r\n      // 检查是否有invoiceId\r\n      if (row.invoiceId) {\r\n        try {\r\n          // 根据invoiceId查找现有发票信息\r\n          const response = await getVatinvoice(row.invoiceId)\r\n          if (response.code === 200 && response.data) {\r\n            // 使用现有发票信息准备数据\r\n            this.prepareInvoiceDataWithExisting(row, response.data)\r\n          } else {\r\n            // 查找失败，使用默认数据\r\n            await this.prepareInvoiceData(row)\r\n          }\r\n        } catch (error) {\r\n          console.error(\"获取发票信息失败:\", error)\r\n          // 出错时使用默认数据\r\n          await this.prepareInvoiceData(row)\r\n        }\r\n      } else {\r\n        // 没有invoiceId，创建新的发票\r\n        await this.prepareInvoiceData(row)\r\n      }\r\n\r\n      this.invoiceDialogVisible = true\r\n    },\r\n\r\n    // 生成发票流水号\r\n    async generateInvoiceCodeNo(rctId, cooperatorId) {\r\n      try {\r\n        // 调用API生成发票编码\r\n        const response = await generateInvoiceCode(rctId, cooperatorId)\r\n        if (response.code === 200) {\r\n          return response.msg\r\n        }\r\n      } catch (error) {\r\n        console.error(\"生成发票编码失败:\", error)\r\n      }\r\n      // 如果API调用失败，返回默认格式\r\n      return \"\"\r\n    },\r\n\r\n    // 准备发票对话框数据（新建发票）\r\n    async prepareInvoiceData(debitNote) {\r\n      // 生成发票流水号\r\n      let invoiceCodeNo\r\n      if (this.rctId && debitNote.clearingCompanyId) {\r\n        let invoiceCode = await this.generateInvoiceCodeNo(this.rctId, debitNote.clearingCompanyId)\r\n        invoiceCodeNo = debitNote.sqdRctNo + \"-\" + invoiceCode\r\n      }\r\n\r\n      // 设置发票表单数据\r\n      this.invoiceForm = {\r\n        // 基本发票信息\r\n        invoiceId: debitNote.invoiceId || null, // 发票ID\r\n        invoiceCodeNo: invoiceCodeNo,\r\n        saleBuy: debitNote.isRecievingOrPaying == 0 ? \"sale\" : \"buy\", // 根据收付标志设置进销项\r\n        taxClass: \"\",\r\n        invoiceType: \"增值税发票\",\r\n        mergeInvoice: debitNote.mergeInvoice || \"0\",\r\n        invoiceOfficalNo: debitNote.invoiceOfficalNo || \"\",\r\n\r\n        // 公司和账户信息\r\n        invoiceBelongsTo: debitNote.companyBelongsTo || \"\",\r\n        richBankCode: debitNote.bankAccountCode || \"\",\r\n        cooperatorId: debitNote.clearingCompanyId || \"\",\r\n        cooperatorBankCode: debitNote.clearingCompanyBankAccount || \"\",\r\n        richCompanyTitle: debitNote.bankAccountName || \"\",\r\n        cooperatorCompanyTitle: debitNote.clearingCompanyName || \"\",\r\n\r\n        // 项目和订单信息\r\n        officalChargeNameSummary: \"\",\r\n        relatedOrderNo: \"\",\r\n\r\n        // 税号和银行信息\r\n        richVatSerialNo: \"\",\r\n        cooperatorVatSerialNo: \"\",\r\n        richBankFullname: \"\",\r\n        cooperatorBankFullname: \"\",\r\n        richBankAccount: \"\",\r\n        cooperatorBankAccount: debitNote.clearingCompanyBankAccount || \"\",\r\n\r\n        // 备注和日期信息\r\n        invoiceRemark: \"\",\r\n        expectedPayDate: debitNote.expectedPaymentDate || \"\",\r\n        approvedPayDate: \"\",\r\n        actualPayDate: debitNote.actualPaymentDate || \"\",\r\n\r\n        // 发票金额信息\r\n        invoiceExchangeRate: \"1\",\r\n        invoiceCurrencyCode: debitNote.dnCurrencyCode || \"RMB\",\r\n        invoiceNetAmount: debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable,\r\n        invoiceStatus: debitNote.invoiceStatus === \"issued\" ? \"1\" : \"0\",\r\n        belongsToMonth: this.formatCurrentMonth(),\r\n\r\n        // RCT关联信息\r\n        rctId: this.rctId || null\r\n      }\r\n\r\n      // 准备发票明细项\r\n      this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {\r\n        return {\r\n          billNo: debitNote.billNo || \"\",\r\n          rctNo: \"\",  // 这里可能需要从父组件获取\r\n          serviceType: charge.chargeName || \"\",\r\n          chargeName: charge.chargeName || \"\",\r\n          remark: charge.remark || \"\",\r\n          paymentFlag: debitNote.isRecievingOrPaying == 0 ? \"收\" : \"付\",\r\n          quoteCurrency: charge.currencyCode || \"\",\r\n          unitPrice: charge.unitPrice || 0,\r\n          quantity: charge.quantity || 0,\r\n          unit: charge.unit || \"\",\r\n          settlementRate: charge.exchangeRate || 1,\r\n          settlementCurrency: debitNote.dnCurrencyCode || \"\",\r\n          taxRate: charge.taxRate || \"\",\r\n          taxIncludedTotal: charge.subtotal || 0,\r\n          invoiceItemName: charge.chargeName || \"\",\r\n          taxCode: \"\"\r\n        }\r\n      }) : []\r\n    },\r\n\r\n    // 准备发票对话框数据（使用现有发票信息）\r\n    prepareInvoiceDataWithExisting(debitNote, existingInvoice) {\r\n      // 使用现有发票信息设置表单数据\r\n      this.invoiceForm = {\r\n        // 基本发票信息\r\n        invoiceId: existingInvoice.invoiceId || debitNote.invoiceId || null,\r\n        invoiceCodeNo: existingInvoice.invoiceCodeNo || debitNote.invoiceCodeNo || \"\",\r\n        saleBuy: existingInvoice.saleBuy || (debitNote.isRecievingOrPaying == 0 ? \"sale\" : \"buy\"),\r\n        taxClass: existingInvoice.taxClass || \"\",\r\n        invoiceType: existingInvoice.invoiceType || \"增值税发票\",\r\n        mergeInvoice: existingInvoice.mergeInvoice || debitNote.mergeInvoice || \"0\",\r\n        invoiceOfficalNo: existingInvoice.invoiceOfficalNo || debitNote.invoiceOfficalNo || \"\",\r\n\r\n        // 公司和账户信息\r\n        invoiceBelongsTo: existingInvoice.invoiceBelongsTo || debitNote.companyBelongsTo || \"\",\r\n        richBankCode: existingInvoice.richBankCode || debitNote.bankAccountCode || \"\",\r\n        cooperatorId: existingInvoice.cooperatorId || debitNote.clearingCompanyId || \"\",\r\n        cooperatorBankCode: existingInvoice.cooperatorBankCode || debitNote.clearingCompanyBankAccount || \"\",\r\n        richCompanyTitle: existingInvoice.richCompanyTitle || debitNote.bankAccountName || \"\",\r\n        cooperatorCompanyTitle: existingInvoice.cooperatorCompanyTitle || debitNote.clearingCompanyName || \"\",\r\n\r\n        // 项目和订单信息\r\n        officalChargeNameSummary: existingInvoice.officalChargeNameSummary || \"\",\r\n        relatedOrderNo: existingInvoice.relatedOrderNo || \"\",\r\n\r\n        // 税号和银行信息\r\n        richVatSerialNo: existingInvoice.richVatSerialNo || \"\",\r\n        cooperatorVatSerialNo: existingInvoice.cooperatorVatSerialNo || \"\",\r\n        richBankFullname: existingInvoice.richBankFullname || \"\",\r\n        cooperatorBankFullname: existingInvoice.cooperatorBankFullname || \"\",\r\n        richBankAccount: existingInvoice.richBankAccount || \"\",\r\n        cooperatorBankAccount: existingInvoice.cooperatorBankAccount || debitNote.clearingCompanyBankAccount || \"\",\r\n\r\n        // 备注和日期信息\r\n        invoiceRemark: existingInvoice.invoiceRemark || \"\",\r\n        expectedPayDate: existingInvoice.expectedPayDate || debitNote.expectedPaymentDate || \"\",\r\n        approvedPayDate: existingInvoice.approvedPayDate || \"\",\r\n        actualPayDate: existingInvoice.actualPayDate || debitNote.actualPaymentDate || \"\",\r\n\r\n        // 发票金额信息\r\n        invoiceExchangeRate: existingInvoice.invoiceExchangeRate || \"1\",\r\n        invoiceCurrencyCode: existingInvoice.invoiceCurrencyCode || debitNote.dnCurrencyCode || \"RMB\",\r\n        invoiceNetAmount: existingInvoice.invoiceNetAmount || (debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable),\r\n        invoiceStatus: existingInvoice.invoiceStatus || (debitNote.invoiceStatus === \"issued\" ? \"1\" : \"0\"),\r\n        belongsToMonth: existingInvoice.belongsToMonth || this.formatCurrentMonth(),\r\n\r\n        // RCT关联信息\r\n        rctId: existingInvoice.rctId || this.rctId || null\r\n      }\r\n\r\n      // 使用现有发票明细项，如果没有则使用debitNote的费用明细\r\n      if (existingInvoice.invoiceItems && existingInvoice.invoiceItems.length > 0) {\r\n        this.invoiceItems = existingInvoice.invoiceItems\r\n      } else {\r\n        // 准备发票明细项\r\n        this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {\r\n          return {\r\n            billNo: debitNote.billNo || \"\",\r\n            rctNo: \"\",  // 这里可能需要从父组件获取\r\n            serviceType: charge.chargeName || \"\",\r\n            chargeName: charge.chargeName || \"\",\r\n            remark: charge.remark || \"\",\r\n            paymentFlag: debitNote.isRecievingOrPaying == 0 ? \"收\" : \"付\",\r\n            quoteCurrency: charge.currencyCode || \"\",\r\n            unitPrice: charge.unitPrice || 0,\r\n            quantity: charge.quantity || 0,\r\n            unit: charge.unit || \"\",\r\n            settlementRate: charge.exchangeRate || 1,\r\n            settlementCurrency: debitNote.dnCurrencyCode || \"\",\r\n            taxRate: charge.taxRate || \"\",\r\n            taxIncludedTotal: charge.subtotal || 0,\r\n            invoiceItemName: charge.chargeName || \"\",\r\n            taxCode: \"\"\r\n          }\r\n        }) : []\r\n      }\r\n    },\r\n\r\n    // 格式化当前月份为 yyyyMM 格式（如 202503）\r\n    formatCurrentMonth() {\r\n      const date = new Date()\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, \"0\")\r\n      return `${year}${month}`\r\n    },\r\n\r\n    // 处理发票对话框提交\r\n    async handleInvoiceSubmit(formData) {\r\n      try {\r\n        let response\r\n\r\n        // 根据是否有invoiceId决定是新增还是修改\r\n        if (formData.invoiceId) {\r\n          // 修改发票\r\n          response = await updateVatinvoice(formData)\r\n        } else {\r\n          // 新增发票\r\n          response = await addVatinvoice(formData)\r\n        }\r\n\r\n        if (response.code === 200) {\r\n          // 更新发票状态\r\n          if (this.currentDebitNote) {\r\n            this.currentDebitNote.invoiceStatus = formData.invoiceStatus === \"1\" ? \"issued\" : \"unissued\"\r\n\r\n            // 将发票ID写入到debitNote中\r\n            let invoiceId = null\r\n            if (response.data && response.data.invoiceId) {\r\n              invoiceId = response.data.invoiceId\r\n              this.currentDebitNote.invoiceId = invoiceId\r\n            } else if (formData.invoiceId) {\r\n              invoiceId = formData.invoiceId\r\n              this.currentDebitNote.invoiceId = invoiceId\r\n            }\r\n\r\n            // 更新发票相关字段\r\n            this.currentDebitNote.invoiceCodeNo = formData.invoiceCodeNo || \"\"\r\n            this.currentDebitNote.invoiceOfficalNo = formData.invoiceOfficalNo || \"\"\r\n            this.currentDebitNote.invoiceType = formData.invoiceType || \"\"\r\n            this.currentDebitNote.mergeInvoice = formData.mergeInvoice || \"0\"\r\n            // 更新分账单的发票状态为已申请\r\n            this.currentDebitNote.invoiceStatus = \"applied\"\r\n\r\n            // 调用接口更新debitNote中的invoiceId\r\n            if (invoiceId && this.currentDebitNote.debitNoteId) {\r\n              try {\r\n                const updateData = {\r\n                  debitNoteId: this.currentDebitNote.debitNoteId,\r\n                  invoiceId: invoiceId,\r\n                  invoiceCodeNo: formData.invoiceCodeNo || \"\",\r\n                  invoiceOfficalNo: formData.invoiceOfficalNo || \"\",\r\n                  invoiceType: formData.invoiceType || \"\",\r\n                  mergeInvoice: formData.mergeInvoice || \"0\",\r\n                  invoiceStatus: \"applied\"\r\n                }\r\n\r\n                // 更新分账单\r\n                const updateResponse = await updateDebitNote(updateData)\r\n                if (updateResponse.code === 200) {\r\n                  this.$message.success(\"发票信息已更新到分账单\")\r\n                } else {\r\n                  console.warn(\"更新分账单发票信息失败:\", updateResponse.msg)\r\n                }\r\n              } catch (updateError) {\r\n                console.error(\"更新分账单发票信息失败:\", updateError)\r\n              }\r\n            }\r\n\r\n            // 通知父组件数据变化\r\n            this.$emit(\"return\", this.debitNoteList)\r\n          }\r\n\r\n          this.$message.success(\"发票保存成功\")\r\n        } else {\r\n          this.$message.error(response.msg || \"发票保存失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"发票保存失败:\", error)\r\n        this.$message.error(\"发票保存失败，请重试\")\r\n      }\r\n\r\n      this.invoiceDialogVisible = false\r\n    },\r\n\r\n    // 处理发票对话框取消\r\n    handleInvoiceCancel() {\r\n      this.invoiceDialogVisible = false\r\n    },\r\n    applyUnlock(row) {\r\n      // 查看发票状态,已开票的不能申请解锁\r\n      if (row.invoiceStatus === \"issued\") {\r\n        this.$message.error(\"已开票的分账单不能申请解锁\")\r\n        return\r\n      }\r\n\r\n      this.$emit(\"applyUnlock\", row)\r\n    },\r\n    setComplete(row) {\r\n      try {\r\n        this.$confirm(\"确定要将该分账单设置为已确认状态吗？此操作将禁用整条数据及其费用明细。\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 修改状态为已确认\r\n          row.billStatus = \"confirmed\"\r\n\r\n          // 设置所有费用明细的isAccountConfirmed为'1'，使其被禁用\r\n          if (row.rsChargeList && row.rsChargeList.length > 0) {\r\n            row.rsChargeList.forEach(charge => {\r\n              charge.isAccountConfirmed = \"1\"\r\n            })\r\n          }\r\n\r\n          // 通知父组件状态变更\r\n          this.$emit(\"setComplete\", row)\r\n\r\n          // 提示用户\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"分账单已设置为已确认状态\"\r\n          })\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        })\r\n      } catch (error) {\r\n        console.error(\"设置分账单状态失败:\", error)\r\n        this.$message.error(\"设置分账单状态失败\")\r\n      }\r\n    },\r\n    changeCurrency(row, currency) {\r\n      row.dnCurrencyCode = currency\r\n    },\r\n    selectBankAccount(row, bankAccount) {\r\n      row.bankAccountCode = bankAccount.bankAccountCode\r\n      row.bankAccountName = bankAccount.bankAccountName\r\n    },\r\n    handleSelectCompany(row, company) {\r\n      row.clearingCompanyId = company.companyId\r\n      row.clearingCompanyName = company.companyShortName\r\n    },\r\n    addDebitNote() {\r\n      this.$emit(\"addDebitNote\")\r\n    },\r\n    currency,\r\n\r\n    // 展开/收起行\r\n    handleExpandChange(row, expandedRows) {\r\n      this.expandedRows = expandedRows\r\n    },\r\n\r\n    // 创建分账单\r\n    async createDebitNote(row) {\r\n      try {\r\n\r\n      } catch (error) {\r\n        console.error(\"创建分账单失败:\", error)\r\n        this.$message.error(\"创建分账单失败\")\r\n      }\r\n    },\r\n    // 删除分账单\r\n    async deleteDebitNote(row) {\r\n      try {\r\n        await this.$confirm(\"确定要删除该分账单吗？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n        this.$emit(\"deleteItem\", row)\r\n      } catch (error) {\r\n        if (error !== \"cancel\") {\r\n          console.error(\"删除分账单失败:\", error)\r\n          this.$message.error(\"删除分账单失败\")\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理费用数据变化\r\n    handleChargeDataChange(row, chargeData) {\r\n      let billReceivable = 0\r\n      let billPayable = 0\r\n\r\n      // 统计chargeData的费用\r\n      if (this.isReceivable) {\r\n        // 应收\r\n        chargeData.forEach(item => {\r\n          // 使用currency.js计算\r\n          billReceivable = currency(billReceivable).add(item.subtotal).toString()\r\n        })\r\n      } else {\r\n        // 应付\r\n        chargeData.forEach(item => {\r\n          billPayable = currency(billPayable).add(item.subtotal).toString()\r\n        })\r\n      }\r\n      row.billReceivable = billReceivable\r\n      row.billPayable = billPayable\r\n\r\n      row.rsChargeList = chargeData\r\n      // 通知父组件数据变化\r\n      this.$emit(\"return\", this.debitNoteList)\r\n    },\r\n\r\n    // 处理费用选择\r\n    handleChargeSelection(row, selectedCharges) {\r\n      const index = this.localDebitNoteList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.localDebitNoteList[index].selectedCharges = selectedCharges\r\n        // 通知父组件数据变化\r\n        this.$emit(\"update:debitNoteList\", this.localDebitNoteList)\r\n      }\r\n    },\r\n\r\n    // 复制费用\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n\r\n    // 删除费用项\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    },\r\n\r\n    // 删除所有费用\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n\r\n    // 获取账单状态类型\r\n    getBillStatusType(status) {\r\n      const statusMap = {\r\n        \"draft\": \"info\",\r\n        \"confirmed\": \"success\",\r\n        \"closed\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取账单状态文本\r\n    getBillStatusText(status) {\r\n      const statusMap = {\r\n        \"draft\": \"草稿\",\r\n        \"confirmed\": \"已确认\",\r\n        \"closed\": \"已关闭\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取销账状态类型\r\n    getWriteoffStatusType(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"info\",\r\n        \"partial\": \"warning\",\r\n        \"written\": \"success\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取销账状态文本\r\n    getWriteoffStatusText(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"未销账\",\r\n        \"partial\": \"部分销账\",\r\n        \"written\": \"已销账\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.charge-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n\r\n  span {\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 覆盖 Element UI 表格样式\r\n:deep(.el-table) {\r\n  .el-table__expanded-cell {\r\n    padding: 0;\r\n\r\n    .expand-content {\r\n      margin: 0;\r\n      border: none;\r\n      background-color: transparent;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}